{"name": "uv-shopivo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 4200", "build": "next build", "start": "next start -p 4200", "lint": "next lint"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.10.3", "@chakra-ui/system": "^2.6.2", "@emotion/react": "^11.13.3", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.13.0", "@mui/icons-material": "^6.1.3", "@mui/material": "^6.1.3", "@mui/material-nextjs": "^6.1.3", "@mui/x-charts": "^7.20.0", "@mui/x-data-grid": "^7.20.0", "@mui/x-date-pickers": "^7.20.0", "@toolpad/core": "^0.7.0", "aws-sdk": "^2.1691.0", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "dayjs": "^1.11.13", "framer-motion": "^11.11.9", "fs": "^0.0.1-security", "html-pdf-node": "^1.0.8", "html-react-parser": "^5.1.18", "html2pdf.js": "^0.10.2", "isomorphic-fetch": "^3.0.0", "jsonwebtoken": "^9.0.2", "localforage": "^1.10.0", "moment": "^2.30.1", "mongodb": "^6.9.0", "next": "14.2.15", "next-auth": "^4.24.8", "nodemailer": "^6.9.16", "number-to-text": "^0.3.9", "path": "^0.12.7", "react": "^18", "react-dom": "^18", "react-icons": "^5.3.0", "react-lottie-player": "^2.1.0", "socket.io": "^4.8.0", "socket.io-client": "^4.8.0", "uuid": "^10.0.0"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "tailwindcss": "^3.4.1"}}