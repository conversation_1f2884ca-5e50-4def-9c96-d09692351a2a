 .

DONE: invoice listte gosteri modal olarak aç.. pdf olarak display et ve send email. -> bunu collapse olarak aç gönderilcek olanın emaili vs....
DONE: Order detayından view invoice u aktif et!.
DONE: gib sync te pdf sync automatically.
DONE: order sync tümünü eşitle..
DONE: fatura gonderim kısmına isim soy isim de girilebilsin.
DONE: settings ekranına User kimlik bilgileri, fatura gönderim cc /bcc ve mail body bilgileri. 
DONE: sent email oldugunda save to db ve order id , invoice id ve gönderilen email.
DONE: gonderilen emailleri fatura modal 'ında göster
DONE: Mail body si db den gelmeli. template olustur.
DONE: gönderilen email i, invoices list tablosuna ekle.
DONE: onaylanmamıssa fatura, email gonderim kismini disable et.
DONE: fatura kesince modal ı kapat. back tusuna basınca onceki order listesine dön. kaldığı yerden devam etsin..
DONE: fatura kesince sadece o order ı local de update et!. güncel invoice verisiyle!.

CANCEL: fatura gönderimi icin de test modu gonderimi.

CANCEL: invoices da custom fatura kesme secenegi.

CANCEL: log sync times - server to local db. display on app.


TODO: Sunucuya at. subdomain olsun. subanet in.


cloud da. wsocket patladı. Starting Socket.IO server on port: 3001
auth submit sonrası redirection ayarları.
token ı localforage ye kaydet. debug icin..
listinvoicescloud vs. calismiyor.

TODO: kargo etiketi.