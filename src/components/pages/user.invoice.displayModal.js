import React, { StrictMode, useState, useEffect, useContext, useRef } from "react";
import moment from "moment";
import localforage from 'localforage';
import parse from 'html-react-parser';
// import html2pdf from 'html2pdf.js';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import useMediaQuery from '@mui/material/useMediaQuery';
import { styled, useTheme } from '@mui/material/styles';
import Grid from '@mui/material/Grid2';
import FormControl from "@mui/material/FormControl"
import FormLabel from "@mui/material/FormLabel"
import TextField from "@mui/material/TextField"
import Typography from "@mui/material/Typography"
import Stack from "@mui/material/Stack"
import MuiCard from "@mui/material/Card"
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import AddBoxIcon from '@mui/icons-material/AddBox';
import LoadingButton from "@mui/lab/LoadingButton"
import AttachEmailIcon from '@mui/icons-material/AttachEmail';
import LinearProgress from '@mui/material/LinearProgress';
import { titlecase, camalize } from '@/lib/fnx/fnx.cli'
import { CardContent, CardHeader } from "@mui/material";
import { useRouter } from 'next/router'
import Collapse from '@mui/material/Collapse';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
// import Checkbox from "@mui/material/Checkbox"
// import Link from "@mui/material/Link"
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import CancelIcon from '@mui/icons-material/Cancel';
// import FormControlLabel from "@mui/material/FormControlLabel"
// import { Container, Divider } from "@mui/material";
// import CustomDatePicker from '@/components/header/CustomDatePicker.js'
const SignInContainer = styled(Stack)(({ theme }) => ({
    minHeight: "100%",
    padding: theme.spacing(2),
    [theme.breakpoints.up("sm")]: {
        padding: theme.spacing(4)
    },
    "&::before": {
        content: '""',
        display: "block",
        position: "absolute",
        zIndex: -1,
        inset: 0,
        backgroundImage:
            "radial-gradient(ellipse at 50% 50%, hsl(210, 100%, 97%), hsl(0, 0%, 100%))",
        backgroundRepeat: "no-repeat",
        ...theme.applyStyles("dark", {
            backgroundImage:
                "radial-gradient(at 50% 50%, hsla(210, 100%, 16%, 0.5), hsl(220, 30%, 5%))"
        })
    }
}))
const Card = styled(MuiCard)(({ theme }) => ({
    display: "flex",
    flexDirection: "column",
    alignSelf: "center",
    width: "100%",
    padding: theme.spacing(4),
    gap: theme.spacing(2),
    margin: "auto",
    [theme.breakpoints.up("sm")]: {
        maxWidth: "450px"
    },
    boxShadow:
        "hsla(220, 30%, 5%, 0.05) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.05) 0px 15px 35px -5px",
    ...theme.applyStyles("dark", {
        boxShadow:
            "hsla(220, 30%, 5%, 0.5) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.08) 0px 15px 35px -5px"
    })
}))
export default function ScrollDialog(props) {
    const router = useRouter();
    // console.log('props dialog', props)
    const { session } = props;
    const { user } = session ? session : {};
    const { token, refreshToken } = user ? user : {};

    const [error, setError] = useState(null)
    const [open, setOpen] = React.useState(false);
    const [showEmailCard, setshowEmailCard] = React.useState(false);
    const [loadingHTML, setloadingHTML] = React.useState(false);
    const [modalData, setmodalData] = React.useState(false);
    const [modalHTMLData, setmodalHTMLData] = React.useState(false);
    const [scroll, setScroll] = React.useState('paper');
    const [maxWidth, setMaxWidth] = React.useState('lg');

    const [loading, setloading] = React.useState(false);
    const [mailAddress, setmailAddress] = React.useState(false);
    const [mailName, setmailName] = React.useState(false);
    const [pdfurl, setpdfurl] = React.useState(false);
    const [EmailGonderimleriRefresh, setEmailGonderimleriRefresh] = useState(0);
    const options = {
        replace(domNode) {
            if (
                domNode instanceof Element &&
                domNode.attribs &&
                domNode.attribs.class === 'remove'
            ) {
                return <></>;
            }
        },
    };

    const contentRef = useRef(null);

    const showSendEmailCard = (act = false) => {
        setshowEmailCard(act);
    };
    const refreshEmailGonderimleri = () => {
        setEmailGonderimleriRefresh(Date.now());
    }

    const convertToPdf = async (belgeNumarasi = 'my-document') => {
        var content = contentRef.current;

        let body = document.body
        let html = document.documentElement
        let height = Math.max(body.scrollHeight, body.offsetHeight,
            html.clientHeight, html.scrollHeight, html.offsetHeight);

        const options = {
            filename: belgeNumarasi + '.pdf',
            margin: 1,
            // image: { type: 'jpeg', quality: 1 },
            // html2canvas: { scale: 1 },
            html2canvas: { dpi: 192, letterRendering: true },
            jsPDF: {
                unit: 'mm',
                // format: 'letter',
                format: [297 * 1.1, 210 * 1.1], // [heightCM, 35.35],
                orientation: 'portrait',
            },
        };

        const html2pdf = (await import('html2pdf.js')).default
        // const html2pdf = new Html2pdf()

        html2pdf().set(options).from(content).save();
        // html2pdf().from(content).save();
    };

    const fetchHTML = async ({
        belgeNumarasi, uuid,
    }) => {
        return new Promise(async function (resolve, reject) {
            try {
                var uri = "/api/db/getinvoicehtml";
                uri += '/' + (belgeNumarasi || uuid);
                // console.info('url', uri);
                const res = await fetch(uri, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'X-Host': 'Subanet.com',
                    },
                })
                if (res) {
                    // console.log('fetchHTML res', res)
                    if (res.status == 200) {
                        const datax = await res.json()
                        resolve(datax.html)
                    } else {
                        console.log('error', res.error?.message)
                        reject(res.error?.message)
                    }

                } else {
                    reject(false)
                }
            }
            catch (e) {
                reject(e)
            }
        });
    }

    useEffect(() => {
        const getH = async () => {
            setloadingHTML(true)
            let htmlData = await fetchHTML({ belgeNumarasi: modalData.belgeNumarasi, uuid: props?.data?.uuid });
            setloadingHTML(false)
            // console.log('props.data', htmlData) //get invoice onayDurumu
            // console.log('fetch modal data', modalData, htmlData, props?.data?.uuid)
            setmodalHTMLData(htmlData)
            setmailAddress(htmlData?.orderData?.invData?.email || htmlData?.invData?.email)
            setmailName(htmlData?.orderData?.invData?.name || htmlData?.invData?.name)
        }
        if (modalData) {
            getH();
        } else {
        }
    }, [modalData])

    const theme = useTheme();
    const fullScreen = useMediaQuery(theme.breakpoints.down('md'));

    const handleClickOpen = (scrollType) => () => {
        setOpen(true);
        props.data && setmodalData(props.data)
        setmailAddress(props.data?.email)
        setmailName(props.data?.aliciUnvanAdSoyad || props.data?.name)
        setScroll(scrollType);
    };

    const handleClose = (event, reason) => {
        if (reason && reason === "backdropClick") {
            return;
        } else {
            setmodalData(false)
            setmailAddress(false)
            // setinvoiceDataR(prepInvoiceData(props.data));
            // console.log('close modal')
            setOpen(false);
        }
    };

    const getUpdateForLocalCache = async () => {
        return new Promise(async (resolve, reject) => {
            const url = `/api/db/listinvoicescloudone/` + (modalData?.ettn || props?.data?.uuid)
            // console.log('getUpdateForLocalCache data', url, modalData?.ettn, props?.data?.uuid)
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'X-Host': 'Subanet.com',
                    },
                });
                if (!response.ok) {
                    reject(false)
                }
                const data = await response.json();
                resolve(data?.data);
                // if (response.statusText === 'OK') {
                // }
                // else {
                // }
            } catch (error) {
                console.log(error);
                reject(error)
            }
        });
    }

    const sendEmail = async () => {
        let data2Post = {};
        data2Post.aliciAdi = mailName || modalData?.aliciUnvanAdSoyad || modalData?.name;
        data2Post.epostaAdresi = mailAddress;
        data2Post.belgeNumarasi = modalData.belgeNumarasi;
        data2Post.uuid = props?.data?.uuid;

        let uri = '/api/db/sendinvoice'
        // console.log('data2Post', data2Post, uri);

        setloading(true)
        if (confirm("Gönderim İşlemi Teyit Ediniz?\n" + mailAddress) == false) {
            setloading(false)
            return false;
        } else {

            try {
                const res = await fetch(uri, {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'X-Host': 'Subanet.com',
                    },
                    body: JSON.stringify(data2Post),
                })
                setloading(false)
                if (!res.ok) {
                    var message = `An error has occured: ${res.status} - ${res.statusText}`;
                    alert(message);
                    return
                }
                const datax = await res.json();
                if (!datax.error) {
                    // console.log('resp', (datax)); //JSON.stringify
                    refreshEmailGonderimleri();
                    let updatedValue = await getUpdateForLocalCache();
                    var invoiceslist = await localforage.getItem('invoiceslist');
                    if (invoiceslist && Array.isArray(invoiceslist.data)) {
                        let invListData = invoiceslist.data;
                        let Idx = invListData.findIndex(i => i.ettn == updatedValue.ettn);
                        if (Idx > -1) {
                            let cuuD = invListData[Idx];
                            invListData[Idx] = {
                                ...cuuD,
                                ...updatedValue,
                            };
                            invoiceslist.data = invListData;
                            await localforage.setItem("invoiceslist", invoiceslist);
                            props.refreshParentTable && props.refreshParentTable()
                        } else {
                            // console.log('updatedValue.ettn', updatedValue.ettn)
                        }
                    } else {
                        // console.log('invoiceslist.data', invoiceslist)
                    }

                    alert('email sent!');
                    // if (res?.url) router.push(res.url)
                } else {
                    console.log('err desc', datax);
                    alert('action failed! \n'); //JSON.stringify(values, null, 2)
                }
            }
            catch (e) {
                setloading(false)
                console.log('e', e)
                alert('Error Code: 981', e)
            }
        }
    }

    const descriptionElementRef = React.useRef(null);
    React.useEffect(() => {
        if (open) {
            // console.log('modal props data', props.data)
            const { current: descriptionElement } = descriptionElementRef;
            if (descriptionElement !== null) {
                descriptionElement.focus();
            }
            if (props.data) {
                // setmodalData(props.data)
                // console.log('open', open)
            }
        }
    }, [open]);

    const handleNameChange = v => {
        setmailName(v?.nativeEvent?.target.value)
    }
    const handleChange = v => {
        setmailAddress(v?.nativeEvent?.target.value)
    }

    const gotoInvoiceID = async (siraID) => {
        var invoiceslist = await localforage.getItem('invoiceslist');
        if (invoiceslist && Array.isArray(invoiceslist.data)) {
            let invListData = invoiceslist.data;
            let Idx = invListData.findIndex(i => i.id == siraID);
            if (Idx > -1) {
                let cuuD = invListData[Idx];
                setmodalData(cuuD)
                setmailAddress(cuuD?.email)
                setmailName(cuuD?.aliciUnvanAdSoyad || cuuD?.name)
            }
        }

    }

    return (
        <React.Fragment>
            <Button onClick={handleClickOpen('paper')}
                // variant="contained"
                size="small"
                disableElevation
                sx={{ padding: 0, px: 2 }}
                startIcon={<OpenInNewIcon />}
            >Göster</Button>
            <Dialog
                open={open}
                onClose={handleClose}
                scroll={'paper'}
                aria-labelledby="scroll-dialog-title"
                aria-describedby="scroll-dialog-description"
                fullWidth={true}
                maxWidth={fullScreen ? 'xl' : maxWidth}
            >
                <DialogTitle id="scroll-dialog-title">
                    <Stack sx={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="h6">
                            Fatura Bilgileri
                        </Typography>
                        <CardPreNextInvoice data={modalData} gotoInvoiceID={gotoInvoiceID} />
                        <CancelIcon sx={{ fontSize: 30 }} onClick={() => handleClose()}></CancelIcon>

                    </Stack>
                </DialogTitle>
                {loadingHTML && <Box sx={{ position: 'absolute', top: 0, left: 0, right: 0, height: 2 }}><LinearProgress /></Box>}
                <>
                    <DialogContent dividers={scroll === 'paper'}>
                        <Grid
                            container
                            spacing={2}
                            columns={12}
                            sx={{ mb: theme => theme.spacing(2), minHeight: '600px' }}
                        >
                            <Grid size={{ xs: 12, sm: 9, lg: 9 }} sx={{
                                // overflow: 'auto',
                            }}>

                                {modalHTMLData &&
                                    <MuiCard sx={{ backgroundColor: 'white' }}>

                                        <Box
                                            sx={{
                                                display: "flex",
                                                flexDirection: "column",
                                                width: "100%",
                                                gap: 2,
                                                borderTopWidth: 1,
                                                pt: 1,
                                            }}>

                                            <Box sx={{ alignSelf: 'flex-end' }}>

                                                <LoadingButton sx={{ color: '#000' }}
                                                    component="label" size="small"
                                                    // variant="contained"
                                                    tabIndex={-1}
                                                    onClick={() => convertToPdf(modalData?.belgeNumarasi || modalData?.uuid)}
                                                    startIcon={<PictureAsPdfIcon />}
                                                >PDF indir</LoadingButton>
                                                {/*
                                                <Typography sx={{ fontSize: 10, mx: 2, px: 2, cursor: 'pointer', borderWidth: 0.5, backgroundColor: '#cecece' }} onClick={async () => {
                                                    convertToPdf(modalData?.belgeNumarasi || modalData?.uuid)
                                                }}>
                                                    PDF indir
                                                </Typography> */}
                                            </Box>

                                        </Box>
                                        <StrictMode>
                                            <div ref={contentRef}>
                                                {parse(modalHTMLData?.invoiceHTML ? modalHTMLData?.invoiceHTML.replaceAll("border-width: 2px", "border-width: 1px").replaceAll("border-color: black;", "border-color: #cecece;") : undefined, options)}
                                            </div>
                                        </StrictMode>
                                    </MuiCard>
                                }
                            </Grid>

                            <Grid size={{ xs: 12, sm: 3, lg: 3 }} sx={{
                                backgroundColor: '#efefef', p: 1
                            }}>
                                <MuiCard sx={{ mb: 2 }}>
                                    <Box>
                                        {modalData?.aliciUnvanAdSoyad || modalData?.name}
                                    </Box>
                                    <Box>
                                        {modalData?.belgeNumarasi ? modalData?.belgeNumarasi + ' / ' : ''}{modalData?.belgeTarihi || modalData?.date}
                                    </Box>

                                </MuiCard>

                                <MuiCard sx={{ mb: 2 }}>
                                    <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                                        <Box>
                                            <Typography variant="body1" gutterBottom sx={{ display: 'block', fontWeight: "600" }}>Email Gönderimleri</Typography>
                                        </Box>
                                        <Box>
                                            {pdfurl && (
                                                <>
                                                    <PictureAsPdfIcon onClick={() => window.open(pdfurl, '_blank')} sx={{ mr: 2, cursor: 'pointer' }} />
                                                </>
                                            )}
                                        </Box>
                                    </Stack> 
                                    <Box sx={{ maxHeight: '300px', overflow: 'auto' }}>
                                        {modalData && <CardEmailed {...props} token={token}
                                            modalData={modalData}
                                            setpdfurl={setpdfurl}
                                            onayDurumu={modalHTMLData?.onayDurumu}
                                            EmailGonderimleriRefresh={EmailGonderimleriRefresh}
                                            showSendEmailCard={showSendEmailCard} />
                                        }
                                    </Box>

                                </MuiCard>

                                <Collapse in={showEmailCard}>
                                    <MuiCard sx={{ mb: 2 }}>
                                        <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                                            <Box sx={{ py: 1 }}>
                                                <Typography variant="body1" gutterBottom sx={{ display: 'block', fontWeight: "600" }}>{'Fatura Kopyası Gönder'}</Typography>
                                            </Box>
                                            <CancelIcon onClick={() => showSendEmailCard(false)}></CancelIcon>

                                        </Stack>
                                        <Box
                                            sx={{
                                                display: "flex",
                                                flexDirection: "column",
                                                width: "100%",
                                                gap: 2,
                                                borderTopWidth: 1,
                                                pt: 1,
                                            }}>
                                            {/* {modalHTMLData?.orderData?.invData?.email || modalData?.email} */}

                                            <FormControl>
                                                <FormLabel htmlFor="EpostaAliciAdi" sx={{ p: 0, m: 0, py: 0.5 }}>Eposta Adresi</FormLabel>
                                                <TextField
                                                    // error={nameError}
                                                    // helperText={nameErrorMessage}
                                                    id="EpostaAliciAdi"
                                                    type="text"
                                                    name="EpostaAliciAdi"
                                                    placeholder="Müşteri Adi "
                                                    autoComplete="name"
                                                    autoFocus
                                                    required
                                                    fullWidth
                                                    // variant="outlined"
                                                    // color={nameError ? "error" : "primary"}
                                                    value={mailName || ''}
                                                    // defaultValue={invoiceData?.name ||  ''}
                                                    sx={{ ariaLabel: "EpostaAliciAdi" }}
                                                    onChange={(e) => handleNameChange(e)}
                                                />
                                            </FormControl>
                                            <FormControl>
                                                <FormLabel htmlFor="emailAddress" sx={{ p: 0, m: 0, py: 0.5 }}>Eposta Adresi</FormLabel>
                                                <TextField
                                                    // error={nameError}
                                                    // helperText={nameErrorMessage}
                                                    id="emailAddress"
                                                    type="text"
                                                    name="emailAddress"
                                                    placeholder="Müşteri eposta adresi"
                                                    autoComplete="email"
                                                    autoFocus
                                                    required
                                                    fullWidth
                                                    // variant="outlined"
                                                    // color={nameError ? "error" : "primary"}
                                                    value={mailAddress || ''}
                                                    // defaultValue={invoiceData?.name ||  ''}
                                                    sx={{ ariaLabel: "name" }}
                                                    onChange={(e) => handleChange(e)}
                                                />
                                            </FormControl>
                                            <Box sx={{ alignSelf: 'flex-end' }}>
                                                <LoadingButton sx={{ backgroundColor: '#ffcc00', color: '#000' }}
                                                    component="label" size="small"
                                                    role={undefined}
                                                    // variant="contained"
                                                    tabIndex={-1}
                                                    onClick={() => sendEmail()}
                                                    loading={loading}
                                                    disabled={loading}
                                                    startIcon={<AttachEmailIcon />}
                                                >Gönder</LoadingButton>
                                            </Box>

                                        </Box>

                                    </MuiCard>
                                </Collapse>
                                {!loadingHTML && <MuiCard sx={{ mb: 2 }}>
                                    {modalHTMLData?.orderData?.orderid && (
                                        <Stack sx={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                                            <Box sx={{ mb: 0 }}>
                                                <Typography variant="caption" gutterBottom sx={{ display: 'block' }}>Sipariş No: </Typography>
                                                <Typography variant="body1" gutterBottom sx={{ display: 'block' }}>{modalHTMLData?.orderData?.orderid}</Typography>
                                            </Box>

                                            <Box>
                                                <Button sx={{ backgroundColor: '#cecece', }}
                                                    component="label" size="small"
                                                    role={undefined}
                                                    // variant="contained"
                                                    tabIndex={-1}
                                                    onClick={() => window.open('/orders/order/' + modalHTMLData?.orderData?.orderid, '_blank')}
                                                    startIcon={<OpenInNewIcon />}
                                                >Git</Button>
                                            </Box>
                                        </Stack>
                                    )}
                                    {!modalHTMLData?.orderData?.orderid && (
                                        <>
                                            <Box>
                                                İlişkili sipariş bilgisi bulunamadı!
                                                <br />
                                            </Box>
                                        </>
                                    )}
                                </MuiCard>
                                }

                                {!loadingHTML && <MuiCard>
                                    {modalHTMLData?.orderData?.invData && (
                                        <>
                                            <Box>
                                                Invoice data
                                            </Box>

                                            <Box>
                                                {JSON.stringify(modalHTMLData?.orderData?.invData)}
                                            </Box>
                                        </>
                                    )}
                                    {!modalHTMLData?.orderData?.invData && (
                                        <>
                                            <Box>
                                                Fatura bilgisi bulunamadı!
                                            </Box>
                                        </>
                                    )}
                                </MuiCard>}

                            </Grid>

                        </Grid>

                    </DialogContent>
                    <DialogActions>
                        <Button onClick={() => handleClose()}>Kapat</Button>
                        {/* <LoadingButton loading={loading} disabled={loading} onClick={console.log}>GIB e gönder</LoadingButton> */}
                    </DialogActions>
                </>
            </Dialog>
        </React.Fragment>
    );
}

const CardEmailed = props => {
    const { modalData } = props;
    const [data, setdata] = React.useState(false);
    const [emails, setemails] = React.useState(false);
    const [loading, setloading] = React.useState(false);
    const [refreshMe, setrefreshMe] = React.useState(0);

    useEffect(() => {
        if (props.modalData) {
            setdata(props.modalData);
        }
    }, [props.modalData]);

    const getemails = async () => {
        return new Promise(async (resolve, reject) => {
            // console.log('getemails data', data)
            const url = `/api/db/getemailedinvoices/` + (data?.ettn || data?.uuid)
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + props.token,
                        'X-Host': 'Subanet.com',
                    },
                });

                if (!response.ok) {
                    reject(false)
                }
                const data = await response.json();
                resolve(data?.data);
                // if (response.statusText === 'OK') {
                // }
                // else {
                // }
            } catch (error) {
                console.log(error);
                reject(error)
            }
        });
    }

    useEffect(() => {
        const start = async () => {
            setloading(true);
            try {
                let edata = await getemails();
                props.setpdfurl(edata?.pdf)
                setemails(edata);
                setloading(false);
            } catch (e) {
                console.log('getemails fetch error', e)
                setloading(false);
            }
        };
        data && start();
    }, [data]);
    useEffect(() => {
        const start = async () => {
            setloading(true);
            try {
                let edata = await getemails();
                props.setpdfurl(edata?.pdf)
                setemails(edata);
                setloading(false);
            } catch (e) {
                console.log('getemails fetch error', e)
                setloading(false);
            }
        };

        if (props.EmailGonderimleriRefresh !== refreshMe) {
            setrefreshMe(props.EmailGonderimleriRefresh);
            start();
        }
    }, [props.EmailGonderimleriRefresh])
    return (
        <>
            {loading && <Box sx={{ position: 'relative', top: 0, left: 0, right: 0, height: 2 }}><LinearProgress /></Box>}

            {!loading && ((emails && emails.emails && Array.isArray(emails.emails) && emails.emails.length == 0) || !emails) && (
                <Typography sx={{ py: 2 }}>
                    Gönderim yapılmamış
                </Typography>
            )
            }
            {props.onayDurumu == 'Onaylandi' && (
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        width: "100%",
                        gap: 2,
                        borderTopWidth: 1,
                        pt: 2,
                    }}>
                    <Button sx={{
                        backgroundColor: '#cecece',
                        borderWidth: 2, borderColor: 'black', color: '#000',
                        alignSelf: 'flex-end'
                    }}
                        component="label" size="small"
                        tabIndex={-1}
                        onClick={() => props.showSendEmailCard(true)}
                        startIcon={<AttachEmailIcon />}
                    >Email Gönder</Button>
                </Box>
            )}

            {!loading && emails && emails.emails && Array.isArray(emails.emails) && emails.emails.length !== 0 && emails.emails.map((e, ix) => {
                return (
                    <Stack key={ix.toString()} sx={{ my: 1, backgroundColor: 'white', p: 1, borderRadius: 1 }}>
                        {e.epostaAdresi}
                        <Typography fontStyle={'italic'} fontSize={'10px'}>{ix + 1}&nbsp;@{e.dtCreated}</Typography>
                    </Stack>
                )
            })
            }

        </>
    )
}

const CardPreNextInvoice = props => {
    const [modalData, setmodalData] = React.useState(false);
    const [aID, setaID] = React.useState({
        once: 0,
        sonra: 0,
    });
    useEffect(() => {
        if (props.data) {
            let d = props.data;
            let dID = d.id;
            let aIDStg = {
                once: dID - 1,
                sonra: dID + 1,
            };
            setaID(aIDStg)
            setmodalData(d);
        } else {
        }
    }, [props.data])

    return (
        <>
            {modalData.id && (
                <>
                    <Stack sx={{ flexDirection: 'row' }}>
                        <Box sx={{ px: 2, width: '150px', }}>
                            {aID?.once !== 0 && (
                                <Typography sx={{ 
                                    py: 0.5, borderWidth: 1, borderColor: '#ccc', borderRadius: 2,
                                    cursor: 'pointer', textAlign: 'center'
                                }} onClick={() => props.gotoInvoiceID && props.gotoInvoiceID(aID.once)}>Önceki Fatura</Typography>
                            )}
                        </Box>
                        <Box sx={{ px: 2, width: '150px', }}>
                            {aID?.sonra !== 0 && (
                                <Typography sx={{
                                    py: 0.5, borderWidth: 1, borderColor: '#ccc', borderRadius: 2,
                                    cursor: 'pointer', textAlign: 'center'
                                }} onClick={() => props.gotoInvoiceID && props.gotoInvoiceID(aID.sonra)}>Sonraki Fatura</Typography>
                            )}
                        </Box>
                    </Stack>
                </>
            )}
        </>
    )
}