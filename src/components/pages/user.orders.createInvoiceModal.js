import React, { useState, useEffect, useContext, useRef } from "react";
import moment from "moment";
import localforage from 'localforage';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import useMediaQuery from '@mui/material/useMediaQuery';
import { styled, useTheme } from '@mui/material/styles';
import Grid from '@mui/material/Grid2';
import FormControl from "@mui/material/FormControl"
import FormLabel from "@mui/material/FormLabel"
import TextField from "@mui/material/TextField"
import Typography from "@mui/material/Typography"
import Stack from "@mui/material/Stack"
import MuiCard from "@mui/material/Card"
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import AddBoxIcon from '@mui/icons-material/AddBox';
import LoadingButton from "@mui/lab/LoadingButton"
import {
    Table,
    TableHead,
    TableBody,
    TableRow,
    TableCell,
    TableContainer,
    Paper,
    Chip,
    Tooltip
} from '@mui/material';

import { titlecase, camalize } from '@/lib/fnx/fnx.cli'
import { CardContent, CardHeader } from "@mui/material";
import { useRouter } from 'next/router'

const appSettingKey = "appSetting";
// import Checkbox from "@mui/material/Checkbox"
// import Link from "@mui/material/Link"
// import OpenInNewIcon from '@mui/icons-material/OpenInNew';

// import FormControlLabel from "@mui/material/FormControlLabel"
// import { Container, Divider } from "@mui/material";
// import CustomDatePicker from '@/components/header/CustomDatePicker.js'
const SignInContainer = styled(Stack)(({ theme }) => ({
    minHeight: "100%",
    padding: theme.spacing(2),
    [theme.breakpoints.up("sm")]: {
        padding: theme.spacing(4)
    },
    "&::before": {
        content: '""',
        display: "block",
        position: "absolute",
        zIndex: -1,
        inset: 0,
        backgroundImage:
            "radial-gradient(ellipse at 50% 50%, hsl(210, 100%, 97%), hsl(0, 0%, 100%))",
        backgroundRepeat: "no-repeat",
        ...theme.applyStyles("dark", {
            backgroundImage:
                "radial-gradient(at 50% 50%, hsla(210, 100%, 16%, 0.5), hsl(220, 30%, 5%))"
        })
    }
}))
const Card = styled(MuiCard)(({ theme }) => ({
    display: "flex",
    flexDirection: "column",
    alignSelf: "center",
    width: "100%",
    padding: theme.spacing(4),
    gap: theme.spacing(2),
    margin: "auto",
    [theme.breakpoints.up("sm")]: {
        maxWidth: "450px"
    },
    boxShadow:
        "hsla(220, 30%, 5%, 0.05) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.05) 0px 15px 35px -5px",
    ...theme.applyStyles("dark", {
        boxShadow:
            "hsla(220, 30%, 5%, 0.5) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.08) 0px 15px 35px -5px"
    })
}))
const prepRefOrderData = orderData => {
    const { id, checkout_id, closed_at, confirmation_number, created_at, currency,
        current_total_price, current_total_tax, name, number, order_number, order_status_url,
        processed_at, reference, token, total_price, updated_at,
    } = orderData;

    return ({
        id, checkout_id, closed_at, confirmation_number, created_at, currency,
        current_total_price, current_total_tax, name, number, order_number, order_status_url,
        processed_at, reference, token, total_price, updated_at
    })
}
const prepInvoiceData = orderData => {
    let resp = {}
    let rndID = (+new Date * Math.random()).toString(36).substring(0, 6);
    resp.id = 'inv_' + rndID;
    resp.dateCreated = new Date(Date.now()).toISOString();
    resp.refOrderData = prepRefOrderData(orderData);
    resp.name = titlecase(orderData?.billing_address?.name);
    resp.fullAddress = titlecase(orderData?.billing_address?.address1) + (orderData?.billing_address?.address2 ? ' ' + titlecase(orderData?.billing_address?.address2) : '');
    resp.district = titlecase(orderData?.billing_address?.province);
    resp.city = orderData?.billing_address?.city ? titlecase(orderData?.billing_address?.city) : '';
    resp.email = orderData?.customer?.email || '';
    resp.phoneNumber = orderData?.customer?.phone || '';

    resp.date = orderData?.created_at ? moment(orderData?.created_at).format('yyyy-MM-DD') : "";
    resp.time = orderData?.created_at ? orderData?.created_at.substr(11, 5) : '';
    resp.taxIDOrTRID = '11111111111';
    resp.taxOffice = orderData?.billing_address?.city ? titlecase(orderData?.billing_address?.city) : '';
    var itemx = [];
    Array.isArray(orderData?.line_items) && orderData?.line_items.map((item, ix) => {
        let randStr = (+new Date * Math.random()).toString(36).substring(0, 6);
        let taxRate = item.tax_lines && Array.isArray(item.tax_lines) && item.tax_lines[0]?.rate && parseFloat(item.tax_lines[0]?.rate);
        let discAmount = item.discount_allocations && Array.isArray(item.discount_allocations) && item.discount_allocations[0]?.amount;

        let urunSatisFiyati = parseFloat(item.price)
        let urunFiyati = item.price && parseFloat(urunSatisFiyati - (discAmount || 0))
        let urunFiyatiKDVsiz = urunFiyati / (1 + taxRate);
        let urunKDVTutari = urunFiyatiKDVsiz * taxRate
        let urunAdedi = item.current_quantity;
        let satirToplamKDVDahilUrunFiyati = urunFiyati * urunAdedi;
        let satirToplamKDVHaricUrunFiyati = urunFiyatiKDVsiz * urunAdedi;
        let satirToplamKDVTutari = urunKDVTutari * urunAdedi

        let itemPrice = item.price && parseFloat(urunSatisFiyati - (discAmount || 0)).toFixed(2);
        let itemTax = item.tax_lines && Array.isArray(item.tax_lines) && parseFloat(item.tax_lines && Array.isArray(item.tax_lines) && item.tax_lines[0]?.price).toFixed(2);
        let itemPriceWOTax = itemPrice / (1 + taxRate); //parseFloat(itemPrice - itemTax).toFixed(2)

        itemx.push({
            id: randStr + '_' + (ix + 1),
            name: titlecase(item.name),
            quantity: urunAdedi,
            urunSatisFiyati,
            discAmount,
            urunFiyati,
            urunFiyatiKDVsiz,
            taxRate,
            urunKDVTutari,
            satirToplamKDVDahilUrunFiyati,
            satirToplamKDVHaricUrunFiyati,
            satirToplamKDVTutari,
        });
    });

    let sumPrice = itemx.reduce((acc, x) => acc + (parseFloat(x.urunFiyati) * x.quantity), 0);
    let sumNPrice = itemx.reduce((acc, x) => acc + (parseFloat(x.urunFiyatiKDVsiz) * x.quantity), 0);
    let sumKDV = itemx.reduce((acc, x) => acc + (parseFloat(x.urunKDVTutari) * x.quantity), 0);
    let discAmounts = itemx.reduce((acc, x) => acc + (parseFloat(x.discAmount) * x.quantity), 0);

    resp.item = itemx;
    resp.sumDiscountAmount = discAmounts;
    resp.sumPrice = sumPrice;
    resp.sumNPrice = sumNPrice;
    resp.sumKDV = sumKDV;
    resp.checks = [
        { checkName: 'total_discounts', result: discAmounts == orderData?.total_discounts },
        { checkName: 'sumPrice', result: sumPrice == orderData?.total_price },
        { checkName: 'sumKDV', result: sumKDV == orderData?.total_tax },
    ]
    return (resp)

}
export default function ScrollDialog(props) {
    const router = useRouter();
    // console.log('props dialog', props)
    const [error, setError] = useState(null)
    const [open, setOpen] = React.useState(false);
    const [scroll, setScroll] = React.useState('paper');
    // const [invoiceLines, setinvoiceLines] = React.useState(false);
    const [maxWidth, setMaxWidth] = React.useState('lg');
    const [invoiceData, setinvoiceDataS] = React.useState(false);
    const [invoiceDataInitial, setinvoiceDataInitial] = React.useState(false);
    const [showInvoicePreview, setshowInvoicePreview] = React.useState(false);

    const [orderData, setorderData] = useState(props.data ? props.data : false);

    const setinvoiceDataR = invData => {
        window.localStorage.setItem('invPrepData', JSON.stringify(invData));
        setinvoiceDataS(invData);
    }

    useEffect(() => {
        if (props.data) {
            // console.log('orderData', props.data?.id)
            setorderData(props.data)
            let invData = prepInvoiceData(props.data)
            setinvoiceDataR(invData);
            setinvoiceDataInitial(invData);
        } else {
        }
    }, [props.data])


    const theme = useTheme();
    const fullScreen = useMediaQuery(theme.breakpoints.down('md'));

    const handleClickOpen = (scrollType) => () => {
        setOpen(true);
        setScroll(scrollType);
    };

    const handleClose = (event, reason) => {
        if (reason && reason === "backdropClick") {
            return;
        } else {
            setinvoiceDataInitial(false);
            // setinvoiceDataR(prepInvoiceData(props.data));
            setinvoiceDataR(false);
            window.localStorage.setItem('invPrepData', null);
            setOpen(false);
        }
    };

    const setinvoiceData = (field, fvalue) => {
        let currFormV = JSON.parse(JSON.stringify(invoiceData));
        if (currFormV) {
            currFormV[field] = fvalue;
            setinvoiceDataR(currFormV);
        }
    }

    const handleLineItemChange = (elementIndex, event, fieldName) => {
        // console.log('zxczc', elementIndex, event, event.target.name, event.target.value)
        let invoiceDataStg = invoiceData
        let invoiceLines = invoiceDataStg.item;
        let lineItems = invoiceLines.map((item, i) => {
            if (elementIndex !== i) return item;
            let xv = {
                ...item,
                [event.target.name]: event.target.value,
            }
            if (event.target.name == 'taxRate') {
                xv.taxRate = (parseFloat(event.target.value) / 100);
            }

            let urunFiyatiKDVsiz = parseFloat(xv.urunFiyati) / (1 + (parseFloat(xv.taxRate)))
            let urunKDVTutari = urunFiyatiKDVsiz * parseFloat(xv.taxRate);
            xv.urunFiyatiKDVsiz = urunFiyatiKDVsiz
            xv.urunKDVTutari = urunKDVTutari;

            xv.urunSatisFiyati = parseFloat(xv.urunFiyati) + (xv.discAmount ? parseFloat(xv.discAmount) : 0)
            // console.log('urunSatisFiyati', titlecase(xv.name), xv.urunSatisFiyati, xv.urunFiyati, xv.discAmount)

            let satirToplamKDVDahilUrunFiyati = parseFloat(xv.urunFiyati) * parseFloat(xv.quantity)
            let satirToplamKDVHaricUrunFiyati = urunFiyatiKDVsiz * parseFloat(xv.quantity)
            let satirToplamKDVTutari = urunKDVTutari * parseFloat(xv.quantity)

            xv.satirToplamKDVDahilUrunFiyati = satirToplamKDVDahilUrunFiyati
            xv.satirToplamKDVHaricUrunFiyati = satirToplamKDVHaricUrunFiyati
            xv.satirToplamKDVTutari = satirToplamKDVTutari
            // return { ...item, [event.target.name]: event.target.value }
            // if (elementIndex !== i) console.log('xc', xv)
            return xv
        })
        // console.log('ixczxc', elementIndex, event.target.name, event.target.value)

        let sumPrice = lineItems.reduce((acc, x) => acc + (parseFloat(x.urunFiyati) * x.quantity), 0);
        let sumNPrice = lineItems.reduce((acc, x) => acc + (parseFloat(x.urunFiyatiKDVsiz) * x.quantity), 0);
        let sumKDV = lineItems.reduce((acc, x) => acc + (parseFloat(x.urunKDVTutari) * x.quantity), 0);
        let discAmounts = lineItems.reduce((acc, x) => acc + (parseFloat(x.discAmount) * x.quantity), 0);

        invoiceDataStg.sumDiscountAmount = discAmounts;
        invoiceDataStg.sumPrice = sumPrice;
        invoiceDataStg.sumNPrice = sumNPrice;
        invoiceDataStg.sumKDV = sumKDV;

        invoiceDataStg.item = lineItems;
        // console.log('lineItems', elementIndex, lineItems, invoiceDataStg)
        setinvoiceDataR(JSON.parse(JSON.stringify(invoiceDataStg)))
    }

    const handleRemoveLineItem = (elementIndex, line) => {
        let invoiceDataStg = invoiceData
        let invoiceLines = invoiceDataStg.item;
        let lineItems = invoiceLines.filter((item, i) => {
            return elementIndex !== i
        })
        invoiceDataStg.item = lineItems;
        setinvoiceDataR(JSON.parse(JSON.stringify(invoiceDataStg)))
    }
    const descriptionElementRef = React.useRef(null);
    React.useEffect(() => {
        if (open) {
            const { current: descriptionElement } = descriptionElementRef;
            if (descriptionElement !== null) {
                descriptionElement.focus();
            }

            if (props.data) {
                setorderData(props.data)
                let invData = prepInvoiceData(props.data)
                setinvoiceDataR(invData);
                setinvoiceDataInitial(invData);
            }

        }
    }, [open]);

    const prepData2Post = dataStg => {
        let resp = dataStg
        return resp;
    }

    const getUpdateForLocalCache = async () => {
        return new Promise(async (resolve, reject) => {
            const url = `/api/db/listorderscloudone/` + (orderData?.id || props?.data?.id)
            // console.log('getUpdateForLocalCache data', url, modalData?.ettn, props?.data?.uuid)
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + props.token,
                        'X-Host': 'Subanet.com',
                    },
                });

                if (!response.ok) {
                    reject(false)
                }
                const data = await response.json();
                resolve(data?.data);
                // if (response.statusText === 'OK') {
                // }
                // else {
                // }
            } catch (error) {
                console.log(error);
                reject(error)
            }
        });
    }

    const [loading, setloading] = React.useState(false);
    const handleSubmit = async event => {
        let keysData = {}

        setloading(true)
        const stickyValue = window.localStorage.getItem(appSettingKey);
        if (stickyValue && stickyValue !== 'null') {
            keysData = (JSON.parse(stickyValue));
        }
        if (keysData?.gibName) {
            let data2Post = {
                keysData: { ...keysData }
            };

            let a_validateInputs = true // validateInputs();
            if (a_validateInputs || false) {
                // if (emailError || passwordError) {
                //     event.preventDefault()
                //     return
                // }
                let stgData = { ...invoiceData };
                Array.isArray(stgData.item) && stgData.item.map(v => {
                    v.unitPrice = parseFloat(v.urunFiyatiKDVsiz);
                    v.price = parseFloat(v.urunFiyatiKDVsiz);
                    v.VATRate = parseFloat(v.taxRate);
                    v.VATAmount = parseFloat(v.urunKDVTutari);
                });
                stgData.totalVAT = stgData.sumKDV;
                stgData.grandTotal = stgData.sumNPrice;
                stgData.grandTotalInclVAT = stgData.sumPrice;
                stgData.paymentTotal = stgData.sumPrice;

                data2Post = {
                    ...data2Post,
                    ...prepData2Post(stgData),
                };
                let uri = '/api/db/createinvoice'
                // let uri = '/api/invoice/gibcreate'
                try {
                    const res = await fetch(uri, {
                        method: 'POST',
                        headers: {
                            'Authorization': 'Bearer ' + props.token,
                            'X-Host': 'Subanet.com',
                        },
                        body: JSON.stringify(data2Post),
                    })
                    if (!res.ok) {
                        var message = `An error has occured: ${res.status} - ${res.statusText}`;
                        alert(message);
                        return
                    }

                    const datax = await res.json();

                    if (!datax.error) {
                        // console.log('resp', (datax)); //JSON.stringify
                        let updatedValue = await getUpdateForLocalCache();
                        // console.log('updatedValue, id:', (updatedValue), orderData?.id); //JSON.stringify
                        var orderlist = await localforage.getItem('orderlist');
                        if (orderlist && Array.isArray(orderlist.data)) {
                            let orderListData = orderlist.data;
                            let Idx = orderListData.findIndex(i => i.orderID == orderData?.id);
                            if (Idx > -1) {
                                let cuuD = orderListData[Idx];
                                orderListData[Idx] = {
                                    ...cuuD,
                                    ...updatedValue,
                                };
                                orderlist.data = orderListData;
                                await localforage.setItem("orderlist", orderlist);
                            } else {
                            }
                        } else {
                        }
                        alert('Invoice saved!');
                        setTimeout(function () {
                            window.location.reload(1);
                        }, 300);

                        // if (res?.url) router.push(res.url)
                    } else {
                        console.log('err desc', datax);
                        alert('battle action failed! \n'); //JSON.stringify(values, null, 2)
                    }
                }
                catch (e) {
                    console.log('e', e)
                    alert('Error Code: 981', e)
                }

                setloading(false)

            } else {
                return
            }

        } else {
            setloading(false)
            alert('please complete settings for GIB account')
        }

    }

    const validateInputs = () => {
        const email = document.getElementById("email")
        const password = document.getElementById("password")

        let isValid = true

        if (!email.value || !/\S+@\S+\.\S+/.test(email.value)) {
            setEmailError(true)
            setEmailErrorMessage("Please enter a valid email address.")
            isValid = false
        } else {
            setEmailError(false)
            setEmailErrorMessage("")
        }

        if (!password.value || password.value.length < 6) {
            setPasswordError(true)
            setPasswordErrorMessage("Password must be at least 6 characters long.")
            isValid = false
        } else {
            setPasswordError(false)
            setPasswordErrorMessage("")
        }
        return isValid
    }

    return (
        <React.Fragment>
            <Button onClick={handleClickOpen('paper')}
                variant="contained"
                startIcon={<AddBoxIcon />}
            >Yeni Fatura</Button>
            <Dialog
                open={open}
                onClose={handleClose}
                scroll={'paper'}
                aria-labelledby="scroll-dialog-title"
                aria-describedby="scroll-dialog-description"
                fullWidth={true}
                maxWidth={fullScreen ? 'xl' : maxWidth}
            >
                <DialogTitle id="scroll-dialog-title">
                    <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Typography variant="h6">
                            Yeni Fatura Oluştur
                        </Typography>
                        <Typography sx={{ fontSize: 10, mx: 2, px: 2, cursor: 'pointer' }} onClick={async () => {
                            // setinvoiceDataR(false)
                            // setTimeout(() => setinvoiceDataR(invoiceDataInitial), 100)
                            setTimeout(() => setinvoiceDataR(prepInvoiceData(props.data)), 100)
                        }}>
                            (sıfırla)
                        </Typography>
                    </Stack>
                </DialogTitle>
                {!showInvoicePreview && (
                    <>

                        <DialogContent dividers={scroll === 'paper'}>
                            <DialogContentText
                                id="scroll-dialog-description"
                                ref={descriptionElementRef}
                                tabIndex={-1}
                                sx={{ pt: 1 }}
                            >
                            </DialogContentText>

                            <Box
                                component="form"
                                onSubmit={handleSubmit}
                                noValidate
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    width: "100%",
                                    // gap: 2
                                }}
                            >
                                {orderData && <InvoiceHeader data={orderData}
                                    invoiceData={invoiceData} setinvoiceData={setinvoiceData} />}
                                {/* <Divider sx={{color: '#333', height: 2, borderWidth: 1}} /> */}

                                {orderData && <InvoiceItems data={orderData}
                                    invoiceData={invoiceData}
                                    handleLineItemChange={handleLineItemChange}
                                    handleRemoveLineItem={handleRemoveLineItem}
                                    setinvoiceData={setinvoiceData} />}
                            </Box>
                            {/* <Box>
                                <Box sx={{ minWidth: '800px', m: 2, wordBreak: 'break-all' }} onClick={() => {
                                    navigator.clipboard.writeText(JSON.stringify(invoiceData));
                                }}>
                                    <pre sx={{ maxWidth: '800px', wordBreak: 'break-all' }} style={{ whiteSpace: "pre-wrap" }}>
                                        <code style={{ fontSize: '10px', color: 'black' }}>{JSON.stringify(invoiceData, null, 4)}</code>
                                    </pre>
                                </Box>
                            </Box> */}

                        </DialogContent>
                        <DialogActions>
                            <Button onClick={handleClose}>Kapat</Button>
                            <Button onClick={() => setshowInvoicePreview(true)}>Ön Gösterim</Button>
                        </DialogActions>
                    </>
                )}
                {showInvoicePreview && (
                    <>
                        <DialogContent dividers={scroll === 'paper'}>
                            <InvoicePreview invoiceData={invoiceData} />
                        </DialogContent>
                        <DialogActions>
                            <Button onClick={() => setshowInvoicePreview(false)}>Geri dön</Button>
                            <LoadingButton loading={loading} disabled={loading} onClick={handleSubmit}>GIB e gönder</LoadingButton>
                        </DialogActions>
                    </>
                )}
            </Dialog>
        </React.Fragment>
    );
}
const InvoiceHeader = props => {
    const [orderData, setorderData] = useState(props.data ? props.data : false);
    const [invoiceData, setinvoiceData] = useState(props.invoiceData ? props.invoiceData : false);
    useEffect(() => {
        setorderData(props.data);
    }, [props.data])

    let refDistrict = useRef(null);
    useEffect(() => {
        setinvoiceData(props.invoiceData);
    }, [props.invoiceData])

    useEffect(() => {
        setinvoiceData(JSON.parse(JSON.stringify(props.invoiceData)));
    }, [props.invoiceData])

    useEffect(() => {
    }, [invoiceData])

    // const handleChange = (field, fvalue) => {
    //     let currFormV = JSON.parse(JSON.stringify(invoiceData));
    //     currFormV[field] = fvalue?.nativeEvent?.target.value;
    //     setinvoiceData(currFormV);
    // }

    const handleChange = (field, fvalue) => {
        props.setinvoiceData(field, fvalue?.nativeEvent?.target.value);
    }
    const [faturaTarihi, setFaturaTarihi] = React.useState(null);

    const [emailError, setEmailError] = React.useState(false)
    const [emailErrorMessage, setEmailErrorMessage] = React.useState("")

    const [nameError, setnameError] = React.useState(false)
    const [nameErrorMessage, setnameErrorMessage] = React.useState("")

    const [addressError, setaddressError] = React.useState(false)
    const [addressErrorMessage, setaddressErrorMessage] = React.useState("")

    const [districtError, setdistrictError] = React.useState(false)
    const [districtErrorMessage, setdistrictErrorMessage] = React.useState("")

    const [cityError, setcityError] = React.useState(false)
    const [cityErrorMessage, setcityErrorMessage] = React.useState("")

    const [phoneNumberError, setphoneNumberError] = React.useState(false)
    const [phoneNumberErrorrMessage, setphoneNumberErrorMessage] = React.useState("")

    const [dateError, setdateError] = React.useState(false)
    const [dateErrorMessage, setdateErrorMessage] = React.useState("")

    const [timeError, settimeError] = React.useState(false)
    const [timeErrorMessage, settimeErrorMessage] = React.useState("")

    const [taxOfficeError, settaxOfficeError] = React.useState(false)
    const [taxOfficeErrorMessage, settaxOfficeErrorMessage] = React.useState("")

    const [taxIDOrTRIDError, settaxIDOrTRIDError] = React.useState(false)
    const [taxIDOrTRIDErrorMessage, settaxIDOrTRIDErrorMessage] = React.useState("")

    const setFaturaTarihiR = trh => {
        setFaturaTarihi(trh)
    }

    return (
        <FormControl>
            <Grid
                container
                spacing={2}
                columns={12}
                sx={{ mb: theme => theme.spacing(1), pb: 2, borderBottomWidth: 1, borderBottomColor: '#333' }}
            >
                <Grid size={{ xs: 12, sm: 8, lg: 8 }}>
                    {/* <HighlightedCard /> */}
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            width: "100%",
                            gap: 2
                        }}>
                        <FormControl>
                            <FormLabel htmlFor="name" sx={{ p: 0, m: 0 }}>Müşteri Adı</FormLabel>
                            <TextField
                                error={nameError}
                                helperText={nameErrorMessage}
                                id="name"
                                type="text"
                                name="name"
                                placeholder="Müşteri adı"
                                autoComplete="name"
                                autoFocus
                                required
                                fullWidth
                                variant="outlined"
                                color={nameError ? "error" : "primary"}
                                value={invoiceData?.name || ''}
                                // defaultValue={invoiceData?.name ||  ''}
                                sx={{ ariaLabel: "name" }}
                                onChange={(e) => handleChange('name', e)}
                            />
                        </FormControl>

                        <FormControl>
                            <FormLabel htmlFor="fullAddress" sx={{ p: 0, m: 0 }}>Fatura Adresi</FormLabel>
                            <TextField
                                error={addressError}
                                size="small"
                                helperText={addressErrorMessage}
                                id="fullAddress"
                                type="text"
                                name="fullAddress"
                                placeholder="fatura adresi"
                                autoComplete="address"
                                autoFocus
                                required
                                fullWidth
                                variant="outlined"
                                // multiline
                                maxRows={2}
                                value={invoiceData?.fullAddress || ''}
                                // defaultValue={invoiceData?.fullAddress ||  ''}
                                color={addressError ? "error" : "primary"}
                                sx={{ ariaLabel: "fullAddress", }}
                                onChange={(e) => handleChange('fullAddress', e)}
                            />
                        </FormControl>

                        <Box
                            sx={{
                                display: "flex",
                                flexDirection: "row",
                                width: "100%",
                                gap: 2
                            }}>
                            <FormControl>
                                <TextField
                                    ref={refDistrict}
                                    error={districtError}
                                    size="small"
                                    helperText={districtErrorMessage}
                                    id="district"
                                    type="text"
                                    name="district"
                                    placeholder="fatura adresi ilce"
                                    autoComplete="district"
                                    autoFocus
                                    required
                                    fullWidth
                                    variant="outlined"
                                    value={invoiceData?.district || ''}
                                    // defaultValue={invoiceData.district}
                                    color={districtError ? "error" : "primary"}
                                    sx={{ ariaLabel: "district", }}
                                    onChange={(e) => handleChange('district', e)}
                                />
                            </FormControl>
                            <FormControl>
                                {/* <FormLabel htmlFor="city">Il</FormLabel> */}
                                <TextField
                                    error={cityError}
                                    size="small"
                                    helperText={cityErrorMessage}
                                    id="city"
                                    type="text"
                                    name="city"
                                    placeholder="fatura adresi ili"
                                    autoComplete="city"
                                    autoFocus
                                    required
                                    fullWidth
                                    variant="outlined"
                                    value={invoiceData.city || ''}
                                    // defaultValue={invoiceData.city ||  ''}
                                    color={cityError ? "error" : "primary"}
                                    sx={{ ariaLabel: "city", }}
                                    onChange={(e) => handleChange('city', e)}
                                />
                            </FormControl>

                        </Box>
                        <Box
                            sx={{
                                display: "flex",
                                flexDirection: "row",
                                width: "100%",
                                gap: 2
                            }}>

                            <FormControl>
                                <FormLabel htmlFor="taxOffice" sx={{ p: 0, m: 0 }}>Vergi Dairesi</FormLabel>
                                <TextField
                                    error={taxOfficeError}
                                    helperText={taxOfficeErrorMessage}
                                    id="taxOffice"
                                    type="text"
                                    name="taxOffice"
                                    placeholder="555xxxx"
                                    autoComplete="taxOffice"
                                    autoFocus
                                    required
                                    fullWidth
                                    variant="outlined"
                                    value={invoiceData?.taxOffice || ''}
                                    // defaultValue={invoiceData?.taxOffice ||  ''}
                                    onChange={(e) => handleChange('taxOffice', e)}
                                    color={taxOfficeError ? "error" : "primary"}
                                    sx={{ ariaLabel: "taxOffice" }}
                                />
                            </FormControl>

                            <FormControl>
                                <FormLabel htmlFor="taxIDOrTRID" sx={{ p: 0, m: 0 }}>Vergi D. No</FormLabel>
                                <TextField
                                    error={taxIDOrTRIDError}
                                    helperText={taxIDOrTRIDErrorMessage}
                                    id="taxIDOrTRID"
                                    type="text"
                                    name="taxIDOrTRID"
                                    placeholder="555xxxx"
                                    autoComplete="taxIDOrTRID"
                                    autoFocus
                                    required
                                    fullWidth
                                    variant="outlined"
                                    value={invoiceData?.taxIDOrTRID || ''}
                                    // defaultValue={invoiceData?.taxIDOrTRID ||  ''}
                                    color={taxIDOrTRIDError ? "error" : "primary"}
                                    onChange={(e) => handleChange('taxIDOrTRID', e)}
                                    sx={{ ariaLabel: "taxIDOrTRID" }}
                                />
                            </FormControl>
                        </Box>
                    </Box>
                </Grid>
                <Grid size={{ xs: 12, sm: 4, lg: 4 }}>
                    {/* <HighlightedCard /> */}
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            width: "100%",
                            gap: 2
                        }}>
                        <FormControl>
                            <FormLabel htmlFor="email" sx={{ p: 0, m: 0 }}>Eposta</FormLabel>
                            <TextField
                                error={emailError}
                                helperText={emailErrorMessage}
                                id="email"
                                type="email"
                                name="email"
                                placeholder="<EMAIL>"
                                autoComplete="email"
                                autoFocus
                                required
                                fullWidth
                                value={invoiceData?.email || ''}
                                // defaultValue={invoiceData?.email || ''}
                                variant="outlined"
                                onChange={(e) => handleChange('email', e)}
                                color={emailError ? "error" : "primary"}
                                sx={{ ariaLabel: "email", }}
                            />
                        </FormControl>
                        <FormControl>
                            <FormLabel htmlFor="phoneNumber" sx={{ p: 0, m: 0 }}>Telefon No</FormLabel>
                            <TextField
                                error={phoneNumberError}
                                helperText={phoneNumberErrorrMessage}
                                id="phoneNumber"
                                type="phoneNumber"
                                name="phoneNumber"
                                placeholder="555xxxx"
                                autoComplete="phoneNumber"
                                autoFocus
                                required
                                fullWidth
                                variant="outlined"
                                value={invoiceData?.phoneNumber || ''}
                                // defaultValue={invoiceData?.phoneNumber || ''}
                                onChange={(e) => handleChange('phoneNumber', e)}
                                color={phoneNumberError ? "error" : "primary"}
                                sx={{ ariaLabel: "phoneNumber" }}
                            />
                        </FormControl>
                        <Grid
                            container
                            spacing={2}
                            columns={12}
                            sx={{ mb: theme => theme.spacing(2) }}
                        >
                            <Grid size={{ xs: 12, sm: 6, lg: 6 }}>

                                <FormLabel htmlFor="date" sx={{ p: 0, m: 0 }}>Fatura Tarihi</FormLabel>
                                {/* {JSON.stringify(orderData?.created_at ? moment(orderData?.created_at).format('yyyy-MM-DD') : "")} */}
                                <TextField
                                    id="date"
                                    name="date"
                                    type="date"
                                    error={dateError}
                                    color={dateError ? "error" : "primary"}
                                    helperText={dateErrorMessage}
                                    placeholder={new Date(Date.now()).toISOString()}
                                    value={moment(invoiceData?.date).format('yyyy-MM-DD') || ''}
                                    // defaultValue={moment(invoiceData?.date).format('yyyy-MM-DD') || ''}
                                    onChange={(e) => handleChange('date', e)}
                                // onChange={e => setFaturaTarihiR(e)}
                                />

                            </Grid>
                            <Grid size={{ xs: 12, sm: 6, lg: 6 }}>
                                <FormLabel htmlFor="time">Saat</FormLabel>
                                {/* {orderData?.created_at.substring(16, 11)} */}
                                <TextField
                                    error={timeError}
                                    helperText={timeErrorMessage}
                                    id="time"
                                    type="time"
                                    name="time"
                                    placeholder="12:01"
                                    autoComplete="time"
                                    autoFocus
                                    required
                                    fullWidth
                                    value={invoiceData?.time || ''}
                                    // defaultValue={invoiceData?.time || ''}
                                    onChange={(e) => handleChange('time', e)}
                                    variant="outlined"
                                    color={timeError ? "error" : "primary"}
                                    sx={{ ariaLabel: "time" }}
                                />
                            </Grid>
                        </Grid>
                    </Box>
                </Grid>
                <Stack>
                    {/* {JSON.stringify(invoiceData)} */}
                </Stack>
            </Grid>
        </FormControl>
    )
}
//http://localhost:3000/orders/order/5022808015003?id=5022808015003&cancelled_at=&checkout_id=25080645681307&closed_at=2024-01-19T18%3A04%3A46%2B03%3A00&confirmed=true&created_at=2024-01-16T13%3A55%3A28%2B03%3A00&currency=TRY&financial_status=paid&fulfillment_status=fulfilled&name=%*********&order_number=2068&order_status_url=https%3A%2F%2Fwww.uniqeravintage.com%2F60131999899%2Forders%2Fee7fb9366e4684f7395a24f8e7ff40d0%2Fauthenticate%3Fkey%3D32c39a160561ca0eaf27f6c8d2257790&total_discounts=0.00&total_line_items_price=1470.00&total_price=1470.00&total_tax=245.00&updated_at=2024-01-19T18%3A04%3A46%2B03%3A00&itemCount=1&cID=5840788783259&cEmail=esraperi%40superonline.com&cPhone=&cEmail2=esraperi%40superonline.com&cPhone2=&cPhoneShip=0530+567+10+18&cFullName=Esra+Peri&cFullNameShip=Esra+Peri&cCity=%C4%B0stanbul&cLat=41.0368374&cLng=29.1188946&invoiceCount=1
// db.getCollection('fact.orders').find({order_number: 2068}).limit(2)
// db.getCollection('fact.orders').aggregate([
//     //{"$project": {line_items: 1, _id: 0}},
//    { $set: { "line_items.order_number": "$order_number" } },
//     {"$unwind": { path: "$line_items" } },
//     { $replaceRoot: { newRoot: "$line_items" } },

//     { $match: { current_quantity: {$gte: 2} } },
// ])

const InvoiceItems = props => {
    const [invoiceData, setinvoiceData] = useState(props.invoiceData ? props.invoiceData : false);
    const [invoiceSumData, setinvoiceSumData] = useState(null);
    const [u, setu] = useState(0);
    const [orderData, setorderData] = useState(props.data ? props.data : false);
    useEffect(() => {
        setorderData(props.data)
    }, [props.data])

    useEffect(() => {
        // setinvoiceData(false)
        setu(Date.now())

        setinvoiceData({
            ...invoiceData,
            ...JSON.parse(JSON.stringify(props.invoiceData))
        });
    }, [props.invoiceData])

    useEffect(() => {
        if (invoiceData.item && Array.isArray(invoiceData.item)) {
            let sumPrice = invoiceData.item.reduce((acc, x) => acc + parseFloat(x.satirToplamKDVDahilUrunFiyati), 0);
            let sumNPrice = invoiceData.item.reduce((acc, x) => acc + parseFloat(x.satirToplamKDVHaricUrunFiyati), 0);
            let sumKDV = invoiceData.item.reduce((acc, x) => acc + parseFloat(x.satirToplamKDVTutari), 0);
            let discAmounts = invoiceData.item.reduce((acc, x) => acc + (x.discAmount ? parseFloat(x.discAmount) : 0), 0);
            let sumPriceBeforeDisc = invoiceData.item.reduce((acc, x) => acc + (parseFloat(x.urunSatisFiyati) * x.quantity), 0);
            setinvoiceSumData({
                sumPriceBeforeDisc,
                sumPrice,
                sumNPrice,
                sumKDV,
                discAmounts,
            })
        }
    }, [invoiceData])

    // const itemsil = (itmID) => {
    //     let currItm = [...invoiceData?.item]; 
    //     let uTaskIndex = currItm.findIndex(t => t.id == itmID);
    //     currItm.splice(uTaskIndex, 1);
    //     console.log('dsds', currItm)
    //     props.setinvoiceData('item', currItm);
    //     // invoiceData?.item = 
    // }

    return (
        <>
            <Typography variant="body1" sx={{ fontWeight: '600' }}>
                Fatura Kalemleri
            </Typography>
            <Grid
                container
                spacing={2}
                columns={12}
                sx={{ mb: theme => theme.spacing(1), pb: 1, borderBottomWidth: 1, borderBottomColor: '#333' }}
            >
                <Grid size={{ xs: 12, sm: 5, lg: 5 }}>
                    Ürün
                </Grid>
                <Grid size={{ xs: 12, sm: 1, lg: 1 }} textAlign={'center'}>
                    Adet
                </Grid>

                <Grid size={{ xs: 12, sm: 2, lg: 2 }} textAlign={'center'}>
                    kdv oran
                </Grid>

                <Grid size={{ xs: 12, sm: 1.5, lg: 1.5 }} textAlign={'center'} title={'kdv dahil '}>
                    urun fiyati*
                </Grid>

                <Grid size={{ xs: 12, sm: 1.5, lg: 1.5 }} textAlign={'center'} title={'kdv dahil '}>
                    toplam tutar*
                </Grid>

                <Grid size={{ xs: 12, sm: 1, lg: 1 }} textAlign={'center'}>
                    &nbsp;
                </Grid>

            </Grid>

            {u > 0 && Array.isArray(invoiceData?.item) && invoiceData?.item.map((item, ix) => {

                return (
                    <Grid
                        key={ix.toString()}
                        container
                        spacing={2}
                        columns={12}
                        sx={{
                            mb: theme => theme.spacing(1),
                            alignItems: 'center', p: { xs: 1, sm: 0, lg: 0 },
                            borderWidth: { xs: 1, sm: 0, lg: 0 },
                            borderRadius: { xs: 1, sm: 0, lg: 0 }
                        }}
                    >
                        <Grid size={{ xs: 12, sm: 5, lg: 5 }}>
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    width: "100%",
                                    gap: 2
                                }}>
                                <FormControl>
                                    <TextField
                                        id="name"
                                        type="text"
                                        name="name"
                                        placeholder="ürün adı"
                                        title="ürün adı"
                                        autoComplete="name"
                                        autoFocus
                                        required
                                        fullWidth
                                        variant="outlined"
                                        color={"primary"}
                                        value={item.name || ""}
                                        // defaultValue={item.name || ""}
                                        onChange={(event) => props.handleLineItemChange(ix, event, "name")}
                                        sx={{ ariaLabel: "name", }}
                                    />
                                </FormControl>
                            </Box>
                        </Grid>
                        <Grid size={{ xs: 12, sm: 1, lg: 1 }} >
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    width: "100%",
                                    gap: 2
                                }}>
                                <FormControl>
                                    <TextField
                                        id="quantity"
                                        type="number"

                                        name="quantity"
                                        placeholder="ürün adedi"
                                        title="ürün adedi"
                                        autoComplete="quantity"
                                        autoFocus
                                        required
                                        fullWidth
                                        variant="outlined"
                                        color={"primary"}
                                        value={item.quantity || ""}
                                        onChange={(event) => props.handleLineItemChange(ix, event, "quantity")}
                                        // defaultValue={item.quantity}
                                        inputProps={{ min: 1, style: { textAlign: 'right' } }}
                                        sx={{ ariaLabel: "quantity" }}
                                    />
                                </FormControl>
                            </Box>


                        </Grid>
                        <Grid size={{ xs: 12, sm: 2, lg: 2 }} >
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "row",
                                    width: "100%",
                                    gap: 2,
                                    alignItems: 'center',
                                    justifyContent: 'flex-end'
                                }}>
                                <span sx={{ width: '70%', textAlign: 'right' }} title={'toplam kdv tutarı: ' + parseFloat(item.satirToplamKDVTutari).toFixed(2)}>
                                    {parseFloat(item.urunKDVTutari).toFixed(2)}
                                </span>

                                <FormControl>
                                    <TextField
                                        id="taxRate"
                                        type="number"
                                        name="taxRate"
                                        placeholder="kdv oranı"
                                        title={'toplam kdv tutarı: ' + parseFloat(item.satirToplamKDVTutari).toFixed(2)}
                                        // title="kdv oranı"
                                        autoComplete="taxRate"
                                        autoFocus
                                        required
                                        fullWidth
                                        variant="outlined"
                                        color={"primary"}
                                        value={item.taxRate * 100 || ""}
                                        onChange={(event) => props.handleLineItemChange(ix, event, "taxRate")}
                                        // defaultValue={item.taxRate * 100}
                                        inputProps={{ min: 0, style: { textAlign: 'right', minWidth: '40px', maxWidth: '60px' } }}
                                        sx={{ ariaLabel: "taxRate" }}
                                    />
                                </FormControl>
                            </Box>

                        </Grid>
                        <Grid size={{ xs: 12, sm: 1.5, lg: 1.5 }} >
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    width: "100%",
                                    gap: 2,
                                }}>
                                <FormControl>
                                    <TextField
                                        id="urunFiyati"
                                        name="urunFiyati"
                                        // disabled
                                        placeholder="KDV hariç ürün fiyatı"
                                        // title="KDV hariç ürün fiyatı"
                                        title={'KDVsiz: ' + parseFloat(item.urunFiyatiKDVsiz).toFixed(2).toString()}
                                        autoComplete="urunFiyati"
                                        autoFocus
                                        required
                                        fullWidth
                                        variant="outlined"
                                        color={"primary"}
                                        onChange={(event) => props.handleLineItemChange(ix, event, "urunFiyati")}
                                        value={(item.urunFiyati) || ""}
                                        // defaultValue={parseFloat(item.urunFiyati).toFixed(2)}
                                        inputProps={{ min: 0, style: { textAlign: 'right', minWidth: '60px', } }}
                                        sx={{ ariaLabel: "urunFiyati", }}
                                    />
                                </FormControl>
                            </Box>
                        </Grid>
                        <Grid size={{ xs: 12, sm: 1.5, lg: 1.5 }}>
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    width: "100%",
                                    gap: 2,
                                }}>
                                <FormControl>
                                    <TextField
                                        id="itemPrice"
                                        disabled
                                        type="number"
                                        name="itemPrice"
                                        placeholder="KDV dahil ürün fiyatı"
                                        // title="KDV dahil ürün fiyatı"
                                        title={'KDVsiz: ' + parseFloat(item.satirToplamKDVHaricUrunFiyati).toFixed(2).toString()}
                                        autoComplete="itemPrice"
                                        autoFocus
                                        required
                                        fullWidth
                                        variant="outlined"
                                        color={"primary"}
                                        value={parseFloat(item.satirToplamKDVDahilUrunFiyati).toFixed(2) || ""}
                                        // defaultValue={parseFloat(item.satirToplamKDVDahilUrunFiyati).toFixed(2)}
                                        inputProps={{ min: 0, style: { textAlign: 'right', minWidth: '70px' } }}
                                        sx={{ ariaLabel: "itemPrice" }}
                                    />
                                </FormControl>
                            </Box>
                        </Grid>

                        <Grid size={{ xs: 12, sm: 1, lg: 1 }}>
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    width: "100%",
                                    gap: 2,
                                }}>
                                <Button size="small"
                                    // onClick={() => itemsil(item.id)}
                                    onClick={() => props.handleRemoveLineItem(ix, item)}
                                    variant="outlined" startIcon={<DeleteForeverIcon />}>
                                    Sil
                                </Button>
                            </Box>
                        </Grid>
                    </Grid>
                )
            })}
            <Grid
                container
                spacing={2}
                columns={12}
                sx={{ mb: theme => theme.spacing(1), pb: 1, borderBottomWidth: 1, borderBottomColor: '#333' }}
            >
                <Grid size={{ xs: 12, sm: 5, lg: 5 }}>

                </Grid>
                <Grid size={{ xs: 12, sm: 1, lg: 1 }} textAlign={'center'}>

                </Grid>

                <Grid size={{ xs: 12, sm: 2, lg: 2 }} textAlign={'center'}>

                </Grid>

                <Grid size={{ xs: 12, sm: 3, lg: 3 }} textAlign={'center'}>
                    <Stack sx={{ flexDirection: 'row', alignItems: 'center', py: 1 }}>
                        <Box sx={{ flex: 3, textAlign: 'right' }}>
                            Ara Toplam:
                        </Box>
                        <Box sx={{ flex: 1, textAlign: 'right', px: 3 }}>

                            {invoiceSumData?.sumPriceBeforeDisc && parseFloat(invoiceSumData?.sumPriceBeforeDisc).toFixed(2)}
                        </Box>
                    </Stack>
                    <Stack sx={{ flexDirection: 'row', alignItems: 'center', py: 1 }}>
                        <Box sx={{ flex: 3, textAlign: 'right' }}>
                            İndirimler Toplam:
                        </Box>
                        <Box sx={{ flex: 1, textAlign: 'right', px: 3 }}>
                            {!isNaN(invoiceSumData?.discAmounts) ? parseFloat(invoiceSumData?.discAmounts).toFixed(2) : '0.00'}
                        </Box>
                    </Stack>
                    <Stack sx={{ flexDirection: 'row', alignItems: 'center', py: 1 }}>
                        <Box sx={{ flex: 3, textAlign: 'right' }}>
                            KDV Hariç :
                        </Box>
                        <Box sx={{ flex: 1, textAlign: 'right', px: 3 }}>
                            {invoiceSumData?.sumNPrice && parseFloat(invoiceSumData?.sumNPrice).toFixed(2)}
                        </Box>
                    </Stack>
                    <Stack sx={{ flexDirection: 'row', alignItems: 'center', py: 1 }}>
                        <Box sx={{ flex: 3, textAlign: 'right' }}>
                            KDV :
                        </Box>
                        <Box sx={{ flex: 1, textAlign: 'right', px: 3 }}>
                            {invoiceSumData?.sumKDV && parseFloat(invoiceSumData?.sumKDV).toFixed(2)}
                        </Box>
                    </Stack>
                    <Stack sx={{ flexDirection: 'row', alignItems: 'center', py: 1 }}>
                        <Box sx={{ flex: 3, textAlign: 'right' }}>
                            Genel Toplam:
                        </Box>
                        <Box sx={{ flex: 1, textAlign: 'right', px: 3 }}>
                            {invoiceSumData?.sumPrice && parseFloat(invoiceSumData?.sumPrice).toFixed(2)}
                        </Box>
                    </Stack>
                </Grid>

                <Grid size={{ xs: 12, sm: 1, lg: 1 }} textAlign={'center'}>

                </Grid>

            </Grid>
        </>
    )
}
const InvoicePreview = props => {
    const [invoiceData, setinvoiceData] = useState(props.invoiceData ? props.invoiceData : false);
    useEffect(() => {
        setinvoiceData(props.invoiceData);
    }, [props.invoiceData])
    return (
        <>
            <Typography variant="h4">Fatura Bilgileri</Typography>
            <Stack sx={{ flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'flex-start', py: 2 }}>
                <MuiCard>
                    <CardHeader>Müşteri</CardHeader>
                    <CardContent>
                        <Typography variant="h6">
                            {invoiceData.name}
                        </Typography>

                        <Typography>
                            {invoiceData.fullAddress}
                        </Typography>
                        <Typography>
                            {invoiceData.district} {invoiceData.city}
                        </Typography>
                        <Typography>
                            {invoiceData.email} {invoiceData.phoneNumber ? ' / ' + invoiceData.phoneNumber : ''}
                        </Typography>
                        <Typography>
                            Vergi Dairesi: {invoiceData.taxOffice} / {invoiceData.taxIDOrTRID}
                        </Typography>
                    </CardContent>
                </MuiCard>
                <MuiCard sx={{ mx: 4 }}>
                    <CardHeader>&nbsp;</CardHeader>
                    <CardContent>
                        <Typography>
                            id: {invoiceData.id}
                        </Typography>
                        <Typography>
                            Tarih: {invoiceData.date} {invoiceData.time}
                        </Typography>
                    </CardContent>
                </MuiCard>
            </Stack>
            <Stack>
                <MuiCard sx={{ mr: 4 }}>
                    <CardContent>
                        <Typography variant="h6">
                            {'Fatura Kalemleri'}
                        </Typography>

                        <Box mt={2} my={1}>
                            <TableContainer component={Paper}>
                                <Table size="small" aria-label="a dense table">
                                    <TableHead sx={{ borderBottomWidth: 2, borderBottomColor: '#999' }}>
                                        <TableRow>
                                            <TableCell sx={{ textAlign: 'left', borderRightWidth: 1 }}>ürün</TableCell>
                                            <TableCell sx={{ textAlign: 'left', borderRightWidth: 1 }}>adet</TableCell>
                                            <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }}>kdv haric tutar</TableCell>
                                            {/* <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }}>kdv hatic tutar</TableCell> */}
                                            <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }}>kdv tutar</TableCell>
                                            <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }}>kdv dahil toplam</TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>

                                        {Array.isArray(invoiceData?.item) && invoiceData?.item.map((item, ix) => {
                                            let kdvOrani = item.taxRate;
                                            return (

                                                <TableRow key={(item.id)} sx={{ borderBottomColor: ix % 2 == 0 ? 'blue' : 'red', borderBottomWidth: 1, my: 1 }}>
                                                    <TableCell sx={{ borderRightWidth: 1 }}>{titlecase(item.name)}</TableCell>
                                                    <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }}>{item.quantity}</TableCell>
                                                    <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }} title={item.urunFiyati}>{item.urunFiyatiKDVsiz.toFixed(2)}</TableCell>
                                                    <TableCell sx={{ textAlign: 'right', mr: 4 }} title={(item.satirToplamKDVTutari).toFixed(2)}>
                                                        <Stack sx={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end' }}>
                                                            <Chip sx={{ mx: 1 }} label={kdvOrani * 100 + '%'} />
                                                            {(item.urunKDVTutari).toFixed(2)}
                                                            {/* {item?.currency} */}
                                                        </Stack>
                                                    </TableCell>
                                                    <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }} title={item.satirToplamKDVHaricUrunFiyati}>{item.satirToplamKDVDahilUrunFiyati.toFixed(2)}</TableCell>
                                                </TableRow>

                                            )
                                        })}
                                        <TableRow key={('Ara1')} sx={{ borderTopColor: '#999', borderTopWidth: 2, my: 3 }}>
                                            <TableCell sx={{ borderRightWidth: 0 }}>&nbsp;</TableCell>
                                            <TableCell sx={{ borderRightWidth: 0 }}>&nbsp;</TableCell>
                                            <TableCell sx={{ borderRightWidth: 1 }}>&nbsp;</TableCell>
                                            <TableCell sx={{ textAlign: 'right', mr: 4 }} >
                                                Ara toplam
                                            </TableCell>
                                            <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }} >{parseFloat(invoiceData.sumNPrice).toFixed(2)}</TableCell>
                                        </TableRow>


                                        <TableRow key={('Ara12')} sx={{ my: 1 }}>
                                            <TableCell sx={{ borderRightWidth: 0 }}>&nbsp;</TableCell>
                                            <TableCell sx={{ borderRightWidth: 0 }}>&nbsp;</TableCell>
                                            <TableCell sx={{ borderRightWidth: 1 }}>&nbsp;</TableCell>
                                            <TableCell sx={{ textAlign: 'right', mr: 4 }} >
                                                KDV toplam
                                            </TableCell>
                                            <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }} >{parseFloat(invoiceData.sumKDV).toFixed(2)}</TableCell>
                                        </TableRow>


                                        <TableRow key={('Ara13')} sx={{ my: 1 }}>
                                            <TableCell sx={{ borderRightWidth: 0 }}>&nbsp;</TableCell>
                                            <TableCell sx={{ borderRightWidth: 0 }}>&nbsp;</TableCell>
                                            <TableCell sx={{ borderRightWidth: 1 }}>&nbsp;</TableCell>
                                            <TableCell sx={{ textAlign: 'right', mr: 4, borderBottomWidth: 1, borderColor: 'inherit' }} >
                                                KDV Dahil Toplam
                                            </TableCell>
                                            <TableCell sx={{ textAlign: 'right', borderRightWidth: 1, borderBottomWidth: 1, borderColor: 'inherit' }} >{parseFloat(invoiceData.sumPrice).toFixed(2)}</TableCell>
                                        </TableRow>


                                    </TableBody>
                                </Table>
                            </TableContainer>
                        </Box>
                    </CardContent>
                </MuiCard>
            </Stack>
            {/* <Stack>
                {JSON.stringify(props.invoiceData)}
            </Stack> */}
        </>

    )
}