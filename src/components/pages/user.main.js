import React, { useEffect, useState, useContext } from 'react'
import Head from 'next/head'
import Stack from "@mui/material/Stack"
import MainGrid from "./user.main.grid.js"
import MainIFrame from "./user.main.iframe.js"
import Layout from "@/lib/layouts/layout.user"
import localforage from 'localforage';
import { ColorModeContext } from "@/lib/context/Provider.themeDarkMode";

import { useColorScheme } from "@mui/material/styles"

export default function Dashboard(props) {
    const lKey = 'gui.darkMode';
    const { session } = props;
    const { user } = session ? session : {};
    user && localforage.setItem('user', {...user, dtCreated: new Date(Date.now()).toISOString()});
    const { token, refreshToken } = user ? user : {};
    const { colorMode, drawerCollapsed, mode: modeX } = useContext(ColorModeContext);
    const { setMode } = useColorScheme()

    useEffect(() => {
        colorMode.setCurrPage('home');
        colorMode.setCurrBread('AnaSayfa/Hosgeldin');
        const stickyValue = window.localStorage.getItem(lKey);
        if (stickyValue && stickyValue !== 'null') {
            setMode(JSON.parse(stickyValue))
        }
    }, []);

    return (
        <>
            <Head>
                <title>Shopivo AnaSayfa</title>
            </Head>
            <Layout session={session} {...props}
                pgTitle={'Pg Titlee'}
                headerPosition='fixed'
            >
                <MainIFrame {...props} />
                
            </Layout>
        </>
    )
}
