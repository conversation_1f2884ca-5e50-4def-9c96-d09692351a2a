import React, { useState, useEffect, useContext } from "react";
import Grid from "@mui/material/Grid2"
import Box from "@mui/material/Box"
import Stack from "@mui/material/Stack"
import Typography from "@mui/material/Typography"
import { useColorScheme } from "@mui/material/styles"
import Copyright from "../internals/Copyright"
import { ColorModeContext } from "@/lib/context/Provider.themeDarkMode";

// import ChartUserByCountry from "./ChartUserByCountry"
// import CustomizedTreeView from "./CustomizedTreeView"
// import CustomizedDataGrid from "./CustomizedDataGrid"
// import HighlightedCard from "./HighlightedCard"
// import PageViewsBarChart from "./PageViewsBarChart"
// import SessionsChart from "./SessionsChart"
// import StatCard from "./StatCard"

const data = [
  {
    title: "Users",
    value: "14k",
    interval: "Last 30 days",
    trend: "up",
    data: [
      200,
      24,
      220,
      260,
      240,
      380,
      100,
      240,
      280,
      240,
      300,
      340,
      320,
      360,
      340,
      380,
      360,
      400,
      380,
      420,
      400,
      640,
      340,
      460,
      440,
      480,
      460,
      600,
      880,
      920
    ]
  },
  {
    title: "Conversions",
    value: "325",
    interval: "Last 30 days",
    trend: "down",
    data: [
      1640,
      1250,
      970,
      1130,
      1050,
      900,
      720,
      1080,
      900,
      450,
      920,
      820,
      840,
      600,
      820,
      780,
      800,
      760,
      380,
      740,
      660,
      620,
      840,
      500,
      520,
      480,
      400,
      360,
      300,
      220
    ]
  },
  {
    title: "Event count",
    value: "200k",
    interval: "Last 30 days",
    trend: "neutral",
    data: [
      500,
      400,
      510,
      530,
      520,
      600,
      530,
      520,
      510,
      730,
      520,
      510,
      530,
      620,
      510,
      530,
      520,
      410,
      530,
      520,
      610,
      530,
      520,
      610,
      530,
      420,
      510,
      430,
      520,
      510
    ]
  }
]


  
export default function MainGrid(props) {

    const {session} = props;
    const { user } = session ? session : {};
    const { token, refreshToken } = user ? user : {};
    const { colorMode, drawerCollapsed, drawerToggled, drawerBroken } = useContext(ColorModeContext);

    const { mode, systemMode, setMode } = useColorScheme()

  return (
    <Box sx={{ width: "100%", maxWidth: { sm: "100%", md: "1700px" }, px: 3 }}>
      {/* cards */}
      <Typography component="h2" variant="h6" sx={{ mb: 2 }}>
        Overview
      </Typography>
      <Grid
        container
        spacing={2}
        columns={12}
        sx={{ mb: theme => theme.spacing(2), minHeight: '600px' }}
      >
        {data.map((card, index) => (
            <Grid key={index} size={{ xs: 12, sm: 6, lg: 3 }} sx={{
                color: 'red'
            }}>
            {/* <StatCard {...card} /> */}
            StatCard: drawer Open: {JSON.stringify(drawerCollapsed)} {JSON.stringify(mode)}
          </Grid>
        ))}
        <Grid size={{ xs: 12, sm: 6, lg: 3 }}>
          {/* <HighlightedCard /> */}
          HighlightedCard
        </Grid>
        <Grid size={{ sm: 12, md: 6 }} sx={{overflow: 'hidden'}}>
          {/* <SessionsChart /> */}
          SessionsChart U
          {JSON.stringify(user, 4, " ")}
        </Grid>
        <Grid size={{ sm: 12, md: 6 }}>
          {/* <PageViewsBarChart /> */}
          PageViewsBarChart
        </Grid>
      </Grid>
      <Typography component="h2" variant="h6" sx={{ mb: 2 }}>
        Details
      </Typography>
      <Grid container spacing={2} columns={12}>
        <Grid size={{ md: 12, lg: 9 }}>
          {/* <CustomizedDataGrid /> */}
          CustomizedDataGrid
        </Grid>
        <Grid size={{ xs: 12, lg: 3 }}>
          <Stack gap={2} direction={{ xs: "column", sm: "row", lg: "column" }}>
            {/* <CustomizedTreeView /> */}CustomizedTreeView
            {/* <ChartUserByCountry /> */}ChartUserByCountry
          </Stack>
        </Grid>
      </Grid>
      <Copyright sx={{ my: 4 }} />
    </Box>
  )
}
