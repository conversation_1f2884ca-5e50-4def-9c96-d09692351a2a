import React, { useState, useEffect, useContext } from "react";
import Grid from "@mui/material/Grid2"
import Box from "@mui/material/Box"
import Stack from "@mui/material/Stack"
import Typography from "@mui/material/Typography"
import { useColorScheme } from "@mui/material/styles"
import Copyright from "../internals/Copyright"
import { ColorModeContext } from "@/lib/context/Provider.themeDarkMode";
 
export default function MainGrid(props) {

    const {session} = props;
    const { user } = session ? session : {};
    const { token, refreshToken } = user ? user : {};
    const { colorMode, drawerCollapsed, drawerToggled, drawerBroken } = useContext(ColorModeContext);

    const { mode, systemMode, setMode } = useColorScheme()

  return (
    <Box sx={{ width: "100%", maxWidth: { sm: "100%", md: "1700px" }, px: 3 }}>
      {/* cards */}
      <Typography component="h2" variant="h6" sx={{ mb: 2 }}>
        Uniqera BackOffice
      </Typography>
      <Grid
        container
        spacing={2}
        columns={12}
        sx={{ mb: theme => theme.spacing(2), minHeight: '600px' }}
      >
        <Grid size={{ xs: 12, sm: 12, lg: 12 }}>
            <iframe src="https://www.youtube.com/embed/_dQ_QoAQAQk" 
                width="100%"
                height="100%" sandbox="allow-scripts allow-same-origin
                allow-presentation" loading='lazy'
                allowFullScreen ></iframe>

        </Grid> 
      </Grid> 
    </Box>
  )
}
