/* eslint-disable react/jsx-key */
import React, { useEffect, useState, useContext } from 'react'
import { useRouter } from 'next/router'
import List from "@mui/material/List"
import ListItem from "@mui/material/ListItem"
import ListItemButton from "@mui/material/ListItemButton"
import ListItemIcon from "@mui/material/ListItemIcon"
import ListItemText from "@mui/material/ListItemText"
import Stack from "@mui/material/Stack"
import HomeRoundedIcon from "@mui/icons-material/HomeRounded"
import AnalyticsRoundedIcon from "@mui/icons-material/AnalyticsRounded"
import PeopleRoundedIcon from "@mui/icons-material/PeopleRounded"
import AssignmentRoundedIcon from "@mui/icons-material/AssignmentRounded"
import SettingsRoundedIcon from "@mui/icons-material/SettingsRounded"
import InfoRoundedIcon from "@mui/icons-material/InfoRounded"
import HelpRoundedIcon from "@mui/icons-material/HelpRounded"
import Tooltip, { tooltipClasses } from '@mui/material/Tooltip';

const mainListItems = [
  { text: "AnaSayfa", icon: <HomeRoundedIcon fontSize="large" />, url: '/', pID: 'home' },
  { text: "Siparişler", icon: <AssignmentRoundedIcon />, url: '/orders', pID: 'orders' },
  { text: "Siparis2Fatura", icon: <AssignmentRoundedIcon />, url: '/ordersfatura', pID: 'ordersfatura' },
  { text: "Faturalar", icon: <AnalyticsRoundedIcon />, url: '/invoices', pID: 'invoices' },
  // { text: "Analytics", icon: <AnalyticsRoundedIcon /> },
  // { text: "Clients", icon: <PeopleRoundedIcon /> },
]

const secondaryListItems = [
  { text: "Ayarlar", icon: <SettingsRoundedIcon />, url: '/settings', pID: 'settings' },
  // { text: "About", icon: <InfoRoundedIcon /> },
  // { text: "Feedback", icon: <HelpRoundedIcon /> }
]
import { ColorModeContext } from "@/lib/context/Provider.themeDarkMode";

export default function MenuContent({open}) {
  const router = useRouter();
  const { mode, colorMode, currPage = 'home' } = useContext(ColorModeContext);

  const goto = url => {
    url && router.push(url);
  }
  return (
    <Stack sx={{ flexGrow: 1, p: 0, justifyContent: "space-between" }}>
      <List dense>
        {mainListItems.map((item, index) => (
          <Tooltip placement="right" key={index.toString()} title={open ? undefined : item.text} 
            slotProps={{
              popper: {
                sx: {
                  [`&.${tooltipClasses.popper}[data-popper-placement*="right"] .${tooltipClasses.tooltip}`]:
                  {
                    marginLeft: '-5px',
                  },
                  [`&.${tooltipClasses.popper}[data-popper-placement*="left"] .${tooltipClasses.tooltip}`]:
                  {
                    marginRight: '0px',
                  },
                },
              },
            }}
            enterDelay={100} leaveDelay={100}>
            <ListItem key={index} disablePadding sx={{ display: "block" }}>
              <ListItemButton sx={[{ minHeight: 36, },// px: 2.5,
              open ? { justifyContent: 'initial', } : { justifyContent: 'center', },
              ]} selected={item.pID == currPage} onClick={() => goto(item?.url)}>
                <ListItemIcon
                  sx={[
                    { minWidth: 0, justifyContent: 'center', pl: 1, },
                    open ? { mr: 1, } : { mr: 'auto', },
                  ]} >{item.icon}</ListItemIcon>
                <ListItemText
                  sx={[{ fontSize: '24px' },
                  open ? { opacity: 1, } : { opacity: {xs: 1, md: 0, }, },
                  ]} primary={item.text} />
              </ListItemButton>
            </ListItem>
          </Tooltip>
          
        ))}
      </List>

      <List dense>
        {secondaryListItems.map((item, index) => (
          <Tooltip key={index} placement="right" title={open ? undefined : item.text}
            slotProps={{
              popper: {
                sx: {
                  [`&.${tooltipClasses.popper}[data-popper-placement*="right"] .${tooltipClasses.tooltip}`]:
                  {
                    marginLeft: '-5px',
                  },
                  [`&.${tooltipClasses.popper}[data-popper-placement*="left"] .${tooltipClasses.tooltip}`]:
                  {
                    marginRight: '0px',
                  },
                },
              },
            }}
            enterDelay={100} leaveDelay={100}>

            <ListItem disablePadding sx={{ display: "block" }}>
              <ListItemButton sx={[{ minHeight: 36, },// px: 2.5,
              open
                ? { justifyContent: 'initial', } : { justifyContent: 'center', },
              ]}  selected={item.pID == currPage} onClick={() => goto(item?.url)}>
                <ListItemIcon
                  sx={[
                    { minWidth: 0, justifyContent: 'center', pl: 1, },
                    open ? { mr: 1, } : { mr: 'auto', },
                  ]} >{item.icon}</ListItemIcon>
                <ListItemText
                  sx={[{ fontSize: '24px' },
                  open ? { opacity: 1, } : { opacity: 0, },
                  ]} primary={item.text} />
              </ListItemButton>
            </ListItem>
          </Tooltip>
        ))}
      </List>
    </Stack>
  )
}
