import React, { useState, useEffect, useContext, forwardRef, useImperativeHandle } from "react";
import { styled, useTheme } from '@mui/material/styles';
import Avatar from "@mui/material/Avatar"
import MuiDrawer, { drawerClasses } from "@mui/material/Drawer"
import Box from "@mui/material/Box"
import Stack from "@mui/material/Stack"
import Typography from "@mui/material/Typography"
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import IconButton from '@mui/material/IconButton';

import Divider, { dividerClasses } from "@mui/material/Divider"
import { paperClasses } from "@mui/material/Paper"
import { listClasses } from "@mui/material/List"
import Menu from "@mui/material/Menu"
// import SelectContent from "./SelectContent"
import MenuContent from "./MenuContent"
import CardAlert from "./CardAlert"
import MenuButton from "./MenuButton"
import OptionsMenu from "./OptionsMenu"
import OptionsMenuItems from "./OptionsMenuItems"

import { ColorModeContext } from "@/lib/context/Provider.themeDarkMode";

const drawerWidth = 240
const collapsedDrawerWidth = 65

const DrawerX = styled(MuiDrawer)({
  width: drawerWidth,
  flexShrink: 0,
  boxSizing: "border-box",
  mt: 10,
  [`& .${drawerClasses.paper}`]: {
    width: drawerWidth,
    boxSizing: "border-box"
  }
})

const Drawer = styled(MuiDrawer, { shouldForwardProp: (prop) => prop !== 'open' })(
  ({ theme }) => ({
    width: drawerWidth,
    flexShrink: 0,
    whiteSpace: 'nowrap',
    boxSizing: 'border-box',
    variants: [
      {
        props: ({ open }) => open,
        style: {
          ...openedMixin(theme),
          '& .MuiDrawer-paper': openedMixin(theme),
        },
      },
      {
        props: ({ open }) => !open,
        style: {
          ...closedMixin(theme),
          '& .MuiDrawer-paper': closedMixin(theme),
        },
      },
    ],
  }),
);

const openedMixin = (theme) => ({
  width: drawerWidth,
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  overflowX: 'hidden',
});

const closedMixin = (theme) => ({
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  overflowX: 'hidden',
  width: collapsedDrawerWidth,
  // width: `calc(${theme.spacing(7)} + 1px)`,
  // [theme.breakpoints.up('sm')]: {
  //   width: `calc(${theme.spacing(8)} + 1px)`,
  // },
});

export default function SideMenu(props) {
  const lKey = 'gui.drawerToggled';
  const { session } = props;
  const { user } = session ? session : {};
  const { token, refreshToken } = user ? user : {};

  const { mode, colorMode, currPage, drawerToggled, drawerBroken } = useContext(ColorModeContext);

  const theme = useTheme();
  const [open, setOpen] = React.useState(drawerToggled || true);

  const handleDrawerOpen = () => {
    setOpen(true);
  };

  const handleDrawerClose = (val = undefined) => {
    if (val == undefined) {
      setOpen(!open);
      colorMode.collapseDrawer(!open)
      window.localStorage.setItem(lKey, JSON.stringify(!open));
    } else {
      setOpen(val);
      colorMode.collapseDrawer(val)
      window.localStorage.setItem(lKey, JSON.stringify(val));

    }
  };

  useEffect(() => {
    // colorMode.collapseDrawer(open)
    const stickyValue = window.localStorage.getItem(lKey);
    if (stickyValue && stickyValue !== 'null') {
      // setValue(JSON.parse(stickyValue));
      handleDrawerClose(JSON.parse(stickyValue))
      // console.log('stickyValue2', JSON.parse(stickyValue))
    }
    // console.log('sideMenu is drawerToggled?', drawerToggled)
  }, [])

  useEffect(() => {
    // colorMode.collapseDrawer(open)
  }, [open])

  const [anchorEl, setAnchorEl] = React.useState(null)
  const openMenu = Boolean(anchorEl)
  const handleMenuClick = event => {
    setAnchorEl(event.currentTarget)
  }
  const handleMenuClose = () => {
    setAnchorEl(null)
  }

  return (
    <Drawer
      variant="permanent" open={open}
      sx={{
        display: { xs: "none", md: "block" },
        [`& .${drawerClasses.paper}`]: {
          backgroundColor: "background.paper",
          ml: 0,
        }
      }}
    >
      <Box
        sx={{
          display: "flex",
          mt: "calc(var(--template-frame-height, 0px) + 4px)",
          p: 1.5,
          ml: 0.5,
        }}
      >
        {/* <SelectContent /> */}
        <Stack sx={{ flexDirection: 'row', flexGrow: 1, justifyContent: 'space-between', alignItems: 'center' }}>
          <Box
            sx={[
              { minWidth: 0, justifyContent: 'center', pl: 1, },
              open ? { mr: 1, } : { mr: 'auto', },
            ]} >
            <Typography
              variant="h5"
              sx={{ fontWeight: 500, lineHeight: "16px" }}>
              {open ? 'Shopivo' : 'S'}
            </Typography>
          </Box>

          <DrawerHeader>
            <IconButton onClick={() => handleDrawerClose(!open)} sx={{ p: 0, m: 0, height: 15, mr: -5 }}>
              {!open ? <ChevronRightIcon sx={{ height: 15, ml: -2 }} /> : <ChevronLeftIcon sx={{ height: 15, ml: -2 }} />}
            </IconButton>
          </DrawerHeader>
        </Stack>
      </Box>
      <Divider />

      
      <MenuContent open={open} />
      {open && <CardAlert />}
      <Stack
        direction="row"
        sx={{
          p: 2,
          gap: 1,
          alignItems: "center",
          borderTop: "1px solid",
          borderColor: "divider"
        }}
      >
        {!open && (
          <>
            <MenuButton
              aria-label="Open menu"
              onClick={handleMenuClick}
              sx={{ borderColor: "transparent" }}
            >
              <Avatar
                sizes="small"
                alt={user?.name || user?.userCode}
                src="/static/images/avatar/7.jpg"
                sx={{ width: 36, height: 36 }}
              />
            </MenuButton>
            <Menu
              anchorEl={anchorEl}
              id="menu"
              open={openMenu}
              onClose={handleMenuClose}
              onClick={handleMenuClose}
              transformOrigin={{ horizontal: "right", vertical: "top" }}
              anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
              sx={{
                [`& .${listClasses.root}`]: {
                  padding: "4px"
                },
                [`& .${paperClasses.root}`]: {
                  padding: 0
                },
                [`& .${dividerClasses.root}`]: {
                  margin: "4px -4px"
                }
              }}
            >
              <OptionsMenuItems
              />
            </Menu>
          </>
        )}
        {open && (
          <Avatar
            sizes="small"
            alt={user?.name || user?.userCode}
            src="/static/images/avatar/7.jpg"
            sx={{ width: 36, height: 36 }}
          />
        )}
        <Box sx={[
          { minWidth: 0, justifyContent: 'center', pl: 1, },
          open ? { mr: 1, } : { mr: 'auto', },
        ]}>
          <Typography
            variant="body2"
            sx={{ fontWeight: 500, lineHeight: "16px" }}
          >
            {user?.name || user?.userCode}
          </Typography>
          <Typography variant="caption" sx={{ color: "text.secondary" }}>
          {user?.email || 'na?' }
          </Typography>
        </Box>
        <OptionsMenu />
      </Stack>
    </Drawer>
  )
}


const DrawerHeader = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'flex-end',
  padding: theme.spacing(0, 1),
  // necessary for content to be below app bar
  ...theme.mixins.toolbar,
}));
