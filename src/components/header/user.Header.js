import React, { useState, useEffect, useContext } from "react";
import Box from '@mui/material/Box';
import { styled, useTheme, alpha, useColorScheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import NotificationsRoundedIcon from '@mui/icons-material/NotificationsRounded';
import NavbarBreadcrumbs from './NavbarBreadcrumbs.js';
import MenuButton from './MenuButton';
import ColorModeIconDropdown from './ColorModeIconDropdown';
import Search from './Search';
import { ColorModeContext } from "@/lib/context/Provider.themeDarkMode";
import { Typography } from "@mui/material";
import { red, grey, lightBlue } from '@mui/material/colors';

const drawerWidth = 240
const collapsedDrawerWidth = 65

export default function Header(props) {
  const theme = useTheme();
  const { mode, systemMode, setMode } = useColorScheme()

  var { position = 'static', currPage = 'home', bgcolor } = props;
  const { colorMode, drawerCollapsed, drawerToggled, drawerBroken } = useContext(ColorModeContext);
  bgcolor = bgcolor || (mode === "dark" ? 'hsl(220, 30%, 7%)' : 'hsl(220, 35%, 97%)')
  return (
    <Stack
      spacing={2}
      sx={{
        alignItems: "flex-start",
        //   mx: 3,
        pb: position == 'fixed' ? 8 : 2,
        mt: { xs: 0, md: 0 },
      }}
    >
      <Box component="div" position={position}
        sx={{
          backgroundColor: bgcolor,
          width: position !== 'fixed' ? '100%' : undefined,
          position: { position },
          right: 0,
          left: position == 'fixed' ? (drawerCollapsed ? `${drawerWidth}px` : `${collapsedDrawerWidth}px`) : 0,
          justifyContent: 'flex-start',
          alignItems: 'center', mb: position == 'fixed' ? 14 : 0,
        }}>
        <Stack
          direction="row"
          sx={{
            display: { xs: 'none', md: 'flex' },
            width: '100%',
            alignItems: { xs: 'flex-start', md: 'center' },
            justifyContent: 'space-between',
            maxWidth: { sm: '100%', md: '1700px' },
            pt: 1.5, pb: 1, px: 3,
            boxShadow: 1,
            // borderBottomWidth: 0.5, borderBottomColor: '#000'
          }}
          spacing={2}
          position={'relative'}
        >
          <NavbarBreadcrumbs />
          <Stack direction="row" sx={{
            gap: 1,
            alignItems: { xs: 'flex-start', md: 'center' },
          }}>
            {props.children}
            {/* <Search /> */}
            {/* <CustomDatePicker /> */}
            <MenuButton showBadge aria-label="Open notifications">
              <NotificationsRoundedIcon />
            </MenuButton>
            <ColorModeIconDropdown />
          </Stack>
        </Stack>
      </Box>
    </Stack>
  );
}