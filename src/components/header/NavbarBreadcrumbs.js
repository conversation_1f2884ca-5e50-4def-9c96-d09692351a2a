import React, { useState, useMemo, useContext } from "react";

import { styled } from "@mui/material/styles"
import Typography from "@mui/material/Typography"
import Breadcrumbs, { breadcrumbsClasses } from "@mui/material/Breadcrumbs"
import NavigateNextRoundedIcon from "@mui/icons-material/NavigateNextRounded"
import { ColorModeContext } from "@/lib/context/Provider.themeDarkMode";

const StyledBreadcrumbs = styled(Breadcrumbs)(({ theme }) => ({
  margin: theme.spacing(1, 0),
  [`& .${breadcrumbsClasses.separator}`]: {
    color: (theme.vars || theme).palette.action.disabled,
    margin: 1
  },
  [`& .${breadcrumbsClasses.ol}`]: {
    alignItems: "center"
  }
}))

export default function NavbarBreadcrumbs() {

  const { mode, colorMode, currBread } = useContext(ColorModeContext);

  return (
    <StyledBreadcrumbs
      aria-label="breadcrumb"
      separator={<NavigateNextRoundedIcon fontSize="small" />}
    >
      {currBread && currBread.split('/').map((b, i) => {
        return (
          <Typography key={i.toString()} variant="body1"
            sx={{ color: "text.primary", fontWeight: 600 }}
          >{b}</Typography>
        )
      })}
      &nbsp;
    </StyledBreadcrumbs>
  )
}
