import { signIn, signOut, useSession } from 'next-auth/react'
import Head from 'next/head'
import Image from "next/image";
import localFont from "next/font/local";
import fnx from "@/lib/fnx/fnx.core"

const geistSans = localFont({
  src: "../lib/fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "../lib/fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

import { appvars } from '@/lib/constants'
import GuestHome from '@/components/pages/guest.login.main';
import UserHome from '@/components/pages/user.main.js';
export default function Home(props) {
  const { status, data: session } = useSession({
    required: true,
  });
  if (status === "loading") {
    return ""
  }
  return (
    <>
      <Head>
        <title>Shopivo</title>
      </Head>
      {session ? <UserHome {...props} session={session} /> : <GuestHome {...props} />}
    </>

  );
}

export async function getServerSideProps() {
  return { props: { appvars } }
}
