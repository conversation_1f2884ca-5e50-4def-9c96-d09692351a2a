import React, { StrictMode, useState, useEffect, useContext, useRef } from "react";

import Head from 'next/head'
import { useRouter } from 'next/router'
import Stack from "@mui/material/Stack"
import Layout from "@/lib/layouts/layout.user"
import { ColorModeContext } from "@/lib/context/Provider.themeDarkMode";
import { signIn, signOut, useSession } from 'next-auth/react'
import Box from "@mui/material/Box"
import Typography from "@mui/material/Typography"
import FormControl from "@mui/material/FormControl"
import FormLabel from "@mui/material/FormLabel"
import FormControlLabel from "@mui/material/FormControlLabel"
import TextField from "@mui/material/TextField"
import useMediaQuery from '@mui/material/useMediaQuery';
import { styled, useTheme } from '@mui/material/styles';
import MuiCard from "@mui/material/Card"
import { CardContent, CardHeader } from "@mui/material";
import Button from '@mui/material/Button';
import Switch from '@mui/material/Switch';
import LinearProgress from '@mui/material/LinearProgress';
import { titlecase } from '@/lib/fnx/fnx.cli'
import CloudSyncIcon from '@mui/icons-material/CloudSync';

import localforage from 'localforage';
import parse from 'html-react-parser';

import Grid from '@mui/material/Grid2';
import { Input as BaseInput } from '@mui/base/Input';

const appSettingKey = "appSetting";
export default function Dashboard(props) {
    const { status, data: session } = useSession({
        required: true,
        onUnauthenticated() {
            signIn();
        },
    })

    const { user } = session ? session : {};
    const { token, refreshToken } = user ? user : {};

    const { colorMode, drawerCollapsed } = useContext(ColorModeContext);
    useEffect(() => {
        colorMode.setCurrPage('settings');
        colorMode.setCurrBread('AnaSayfa/Ayarlar');
    }, []);

    if (status === "loading") {
        return "Loading, please wait..."
    }

    return (
        <>
            <Head>
                <title>Shopivo Ayarlar</title>
            </Head>
            <Layout session={session} {...props}
                pgTitle={'Ayarlar'}
                headerPosition='fixedx'
            // headerBgColor={'lightblue'}
            // headerItems={<Typography>HelloTitle</Typography>}
            >
                <Box sx={{ width: "100%", maxWidth: { sm: "100%", md: "1700px" }, px: 3 }}>
                    <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                        <Typography component="h2" variant="h6" >
                            Ayarlar
                        </Typography>
                        {/* <Box sx={{ flexDirection: 'row' }}>
                            <Button sx={{ backgroundColor: '#cecece', mx: 2 }}
                                component="label"
                                role={undefined}
                                variant="contained"
                                tabIndex={-1}
                                onClick={() => syncGIB()}
                                startIcon={<CloudSyncIcon />}
                            >Sync GIB</Button>
                        </Box> */}
                    </Stack>
                    <AyarlarBody {...props} user={user} />
                </Box>
            </Layout>
        </>
    )
}

const AyarlarBody = props => {

    const router = useRouter();
    const [loading, setloading] = React.useState(false);
    return (
        <>
            <Grid
                container
                spacing={2}
                columns={12}
                sx={{ mb: theme => theme.spacing(2), minHeight: '600px' }}
            >
                <Grid size={{ xs: 12, sm: 4, lg: 4 }}>
                    <CardGIB {...props} />
                </Grid>
                <Grid size={{ xs: 12, sm: 4, lg: 4 }}>
                    <CardEmail {...props} />
                </Grid>
                <Grid size={{ xs: 12, sm: 4, lg: 4 }}>
                    <ClearCacheButtons {...props} />
                </Grid>
            </Grid>
        </>
    )
}

const Card = styled(MuiCard)(({ theme }) => ({
    display: "flex",
    flexDirection: "column",
    alignSelf: "center",
    width: "100%",
    padding: theme.spacing(2),
    gap: theme.spacing(2),
    margin: "auto",
    [theme.breakpoints.up("sm")]: {
        maxWidth: "450px"
    },
    boxShadow:
        "hsla(220, 30%, 5%, 0.05) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.05) 0px 15px 35px -5px",
    ...theme.applyStyles("dark", {
        boxShadow:
            "hsla(220, 30%, 5%, 0.5) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.08) 0px 15px 35px -5px"
    })
}))

const CardGIB = props => {
    const [checked, setchecked] = React.useState(false);
    const [gib, setgib] = React.useState(false);
    const [gibForm, setgibForm] = React.useState({});
    useEffect(() => {
        const stickyValue = window.localStorage.getItem(appSettingKey);
        if (stickyValue && stickyValue !== 'null') {
            let vx = JSON.parse(stickyValue);
            setgib(vx)
            setgibForm(vx)
            vx.gibTest == !undefined && setchecked(vx.gibTest)
        }
    }, []);
    const handleChange = v => {
        setchecked(v.target.checked);
        if (v.target.checked) {
            setgibForm({
                gibTest: true,
                gibName: '33333307',
                gibPass: '1'
            })
        } else {
            setgibForm({
                gibTest: false,
                gibName: '',
                gibPass: ''
            })
        }
    }

    const handleX = () => {
        setgibForm({
            gibName: 'AAB03706982', //AAB03706982
            gibPass: 'AAB853241' //AAB853241
        })
    }

    const updateForm = (field, fvalue) => {
        let currForm = {
            ...gibForm,
        };
        currForm[field] = fvalue
        setgibForm(currForm)
    };

    const saveForm = (val = undefined) => {
        let stgGIB = gibForm || {};
        window.localStorage.setItem(appSettingKey, JSON.stringify(stgGIB));
        alert('Kayıt başarılı')
        setgib(JSON.parse(JSON.stringify(stgGIB)))
        return true
    };

    const deleteCache = (val = undefined) => {
        window.localStorage.setItem(appSettingKey, null);
        setgib(false);
        setgibForm(false);
        setchecked(false);
        return true
    };

    return (
        <>
            <Card>
                <CardContent>
                    <Typography variant="h6" onClick={handleX}>
                        GIB Hesap Bilgisi
                    </Typography>
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            width: "100%",
                            gap: 2,
                            my: 2,
                        }}>
                        <FormControl>
                            <FormLabel htmlFor="gibName" sx={{ p: 0, m: 0 }}>GIB Earsiv Kullanıcı Adı</FormLabel>
                            <TextField
                                id="gibName"
                                type="text"
                                name="gibName"
                                placeholder="GIB Earsiv kullanici adı"
                                autoComplete=""
                                autoFocus
                                required
                                fullWidth
                                variant="outlined"
                                color={false ? "error" : "primary"}
                                value={gibForm?.gibName || ''}
                                sx={{ ariaLabel: "gibName" }}
                                onChange={(e) => updateForm('gibName', e?.nativeEvent?.target.value)}
                            />
                        </FormControl>

                        <FormControl>
                            <FormLabel htmlFor="gibPass" sx={{ p: 0, m: 0 }}>GIB Earsiv Parola</FormLabel>
                            <TextField
                                size="small"
                                id="gibPass"
                                type="text"
                                name="gibPass"
                                placeholder="GIB Earsiv parola"
                                autoComplete=""
                                autoFocus
                                required
                                fullWidth
                                variant="outlined"
                                value={gibForm?.gibPass || ''}
                                color={false ? "error" : "primary"}
                                sx={{ ariaLabel: "gibPass", }}
                                onChange={(e) => updateForm('gibPass', e?.nativeEvent?.target.value)}
                            />
                        </FormControl>

                        <FormControl>
                            <FormControlLabel control={
                                <Switch
                                    checked={checked}
                                    onChange={handleChange}
                                    inputProps={{ 'aria-label': 'controlled' }}
                                    color="warning" />
                            } label="Test Modu" />
                        </FormControl>

                        <Box sx={{ alignSelf: 'flex-end' }}>
                            <Button onClick={deleteCache} size='small' sx={{ color: 'black', boxShadow: 1, backgroundColor: 'inherit', mx: 2 }}>
                                Sil
                            </Button>
                            <Button onClick={saveForm} size='small' sx={{ color: 'black', boxShadow: 1, backgroundColor: 'lightgreen' }}>
                                Kaydet
                            </Button>
                        </Box>
                    </Box>
                </CardContent>
            </Card>
        </>
    )
}

const CardEmail = props => {

    const { user = {} } = props;
    const { token, refreshToken } = user;

    const [loading, setloading] = React.useState(false);
    const [sending, setsending] = React.useState(false);
    const [serverData, setserverData] = React.useState(false);
    const [emailForm, setemailForm] = React.useState({});
    const [mounted, setmounted] = React.useState(false);

    const options = {
        replace(domNode) {
            if (
                domNode instanceof Element &&
                domNode.attribs &&
                domNode.attribs.class === 'remove'
            ) {
                return <></>;
            }
        },
    };

    const contentRef = useRef(null);

    const updateForm = (field, fvalue) => {
        let currForm = {
            ...emailForm,
        };
        currForm[field] = fvalue
        setemailForm(currForm)
    };

    const getsettings = async () => {
        return new Promise(async (resolve, reject) => {
            const url = `/api/db/getsettings`
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'X-Host': 'Subanet.com',
                    },
                });
                if (!response.ok) {
                    console.log('error: response.statusText', response)
                    reject(false)
                } 
                const data = await response.json();
                resolve(data?.settings);
                // console.log('getsettings token', token)
                // if (response.statusText === 'OK') {
                   
                // }
                // else {
                // }
            } catch (error) {
                console.log('error', error);
                reject(error)
            }
        });
    }
    useEffect(() => {
        const start = async () => {
            setloading(true);
            let data = await getsettings();
            setserverData(data);
            let currForm = {
                ...emailForm,
                invEmailBody: data.invEmailBody,
                invEmailSubject: data.invEmailSubject,
                invEmailBCC: data.invEmailBCC,
            };
            setemailForm(currForm);
            setloading(false);
        };
        start();
    }, []);


    const handleSubmit = async () => {
        let data2Post = {
            ...emailForm,
            token,
        };

        let uri = '/api/db/setsettings'
        console.log('data2Post', data2Post, uri, props);

        setsending(true)
        if (confirm("Yeni bilgilerin kayıt edilmesini teyit ediniz?\n") == false) {
            setsending(false)
            return false;
        } else {

            try {
                const res = await fetch(uri, {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'X-Host': 'Subanet.com',
                    },
                    body: JSON.stringify(data2Post),
                })
                setsending(false)
                if (!res.ok) {
                    var message = `An error has occured: ${res.status} - ${res.statusText}`;
                    alert(message);
                    return
                }
                const datax = await res.json();
                if (!datax.error) {
                    console.log('resp', (datax)); //JSON.stringify
                    // alert('email sent!')
                    // if (res?.url) router.push(res.url)
                } else {
                    console.log('err desc', datax);
                    alert('action failed! \n'); //JSON.stringify(values, null, 2)
                }
            }
            catch (e) {
                setsending(false)
                console.log('e', e)
                alert('Error Code: 981', e)
            }
        }
    }

    return (
        <>
            <Card>
                <CardContent>
                    <Typography variant="h6" onClick={() => console.log(emailForm)}>
                        Eposta Ayarları
                    </Typography>
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            width: "100%",
                            gap: 2,
                            my: 2,
                        }}>

                        <FormControl>
                            <FormLabel htmlFor="invEmailBCC" sx={{ p: 0, m: 0 }}>E-Fatura Kopya Gönderim Adresi</FormLabel>
                            <TextField
                                id="invEmailBCC"
                                type="email"
                                name="invEmailBCC"
                                placeholder=" "
                                autoComplete=""
                                autoFocus
                                required
                                fullWidth
                                variant="outlined"
                                color={false ? "error" : "primary"}
                                value={emailForm?.invEmailBCC || ''}
                                sx={{ ariaLabel: "invEmailBCC" }}
                                onChange={(e) => updateForm('invEmailBCC', e?.nativeEvent?.target.value)}
                            />
                        </FormControl>

                        <FormControl>
                            <FormLabel htmlFor="invEmailSubject" sx={{ p: 0, m: 0 }}>E-Fatura eposta konu başlığı </FormLabel>
                            <TextField
                                id="invEmailSubject"
                                type="text"
                                name="invEmailSubject"
                                placeholder=" "
                                autoComplete=""
                                required
                                fullWidth
                                variant="outlined"
                                color={false ? "error" : "primary"}
                                value={emailForm?.invEmailSubject || ''}
                                sx={{ ariaLabel: "invEmailSubject" }}
                                onChange={(e) => updateForm('invEmailSubject', e?.nativeEvent?.target.value)}
                            />
                        </FormControl>

                        <FormControl>
                            <FormLabel htmlFor="invEmailBody" sx={{ p: 0, m: 0 }}>E-Fatura eposta metni </FormLabel>
                            <Input
                                aria-label="Demo input"
                                multiline placeholder="Type something…"
                                id="invEmailBody"
                                name="invEmailBody"
                                fullWidth
                                value={emailForm?.invEmailBody || ''}
                                sx={{ ariaLabel: "invEmailBody", width: '100%' }}
                                onChange={(e) => updateForm('invEmailBody', e?.nativeEvent?.target.value)}
                            />
                        </FormControl>
                        {emailForm?.invEmailBody && <Card sx={{ backgroundColor: 'white' }}>

                            <StrictMode>
                                <div ref={contentRef}>
                                    {parse(emailForm?.invEmailBody)}
                                </div>
                            </StrictMode>

                        </Card>}

                        <Box sx={{ alignSelf: 'flex-end' }}>
                            <Button onClick={console.log} size='small' sx={{ color: 'black', boxShadow: 1, backgroundColor: 'inherit', mx: 2 }}>
                                Sil
                            </Button>
                            <Button onClick={handleSubmit} size='small' sx={{ color: 'black', boxShadow: 1, backgroundColor: 'lightgreen' }}>
                                Kaydet
                            </Button>
                        </Box>
                    </Box>
                </CardContent>
            </Card>
        </>
    )
}


const RootDiv = styled('div')`
  display: flex;
  max-width: 100%;
`;

const TextareaElement = styled('textarea', {
    shouldForwardProp: (prop) =>
        !['ownerState', 'minRows', 'maxRows'].includes(prop.toString()),
})(
    ({ theme }) => `
    width: 100%;
    font-family: 'IBM Plex Sans', sans-serif;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5rem;
    padding: 8px 12px;
    border-radius: 8px 8px 0 8px;
    color: ${theme.palette.mode === 'dark' ? grey[300] : grey[900]};
    background: ${theme.palette.mode === 'dark' ? grey[900] : '#fff'};
    border: 1px solid ${theme.palette.mode === 'dark' ? grey[700] : grey[200]};
    box-shadow: 0px 2px 4px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0, 0.5)' : 'rgba(0,0,0, 0.05)'
        };
  
    &:hover {
      border-color: ${blue[400]};
    }
  
    &:focus {
      border-color: ${blue[400]};
      box-shadow: 0 0 0 3px ${theme.palette.mode === 'dark' ? blue[700] : blue[200]};
    }
  
    // firefox
    &:focus-visible {
      outline: 0;
    }
  `,
);

const blue = {
    100: '#DAECFF',
    200: '#80BFFF',
    400: '#3399FF',
    500: '#007FFF',
    600: '#0072E5',
    700: '#0059B2',
};

const grey = {
    50: '#F3F6F9',
    100: '#E5EAF2',
    200: '#DAE2ED',
    300: '#C7D0DD',
    400: '#B0B8C4',
    500: '#9DA8B7',
    600: '#6B7A90',
    700: '#434D5B',
    800: '#303740',
    900: '#1C2025',
};

const Input = React.forwardRef(function CustomInput(props, ref) {
    return (
        <BaseInput
            slots={{
                root: RootDiv,
                input: 'input',
                textarea: TextareaElement,
            }}
            {...props}
            ref={ref}
        />
    );
});

const ClearCacheButtons = props => {
    const cclear = async target => {
        if (confirm(target + " silinmesini teyit ediniz?\n") == false) {
            setsending(false)
            return false;
        } else {
            localforage.removeItem(target);
        }
    }
return (
    <>
        <Card>
            <CardContent>
                <Typography variant="h6" >
                    Hafıza Temizle
                </Typography>

                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        width: "100%",
                        gap: 2,
                        my: 2,
                    }}>
                    <Box>
                        <Button onClick={() => cclear('orderlist')} size='small' sx={{ color: 'black', boxShadow: 1, backgroundColor: 'inherit', mx: 2, my: 1 }}>
                            Sipariş Verilerini Temizle
                        </Button>
                        <Button onClick={() => cclear('invoiceslist')} size='small' sx={{ color: 'black', boxShadow: 1, backgroundColor: 'inherit', mx: 2, my: 1 }}>
                            Fatura Verilerini Temizle
                        </Button>
                    </Box>
                </Box>
            </CardContent>
        </Card>
    </>
)
}