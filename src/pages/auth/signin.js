import * as React from "react"
import { useRouter } from 'next/router'
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import Checkbox from "@mui/material/Checkbox"
import CssBaseline from "@mui/material/CssBaseline"
import FormControlLabel from "@mui/material/FormControlLabel"
import Divider from "@mui/material/Divider"
import FormLabel from "@mui/material/FormLabel"
import FormControl from "@mui/material/FormControl"
import Link from "@mui/material/Link"
import TextField from "@mui/material/TextField"
import Typography from "@mui/material/Typography"
import Stack from "@mui/material/Stack"
import MuiCard from "@mui/material/Card"
import { styled } from "@mui/material/styles"
import ForgotPassword from "./forgotPassword"
import GithubIcon from "./customIcons" //{ GoogleIcon, GithubIcon } 
import { createTheme, ThemeProvider } from '@mui/material/styles';
import AppTheme from "@/lib/styles/AppTheme" 
import { signIn, useSession } from "next-auth/react"
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert';
import Slide from '@mui/material/Slide';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import LinearProgress from '@mui/material/LinearProgress';

const Card = styled(MuiCard)(({ theme }) => ({
    display: "flex",
    flexDirection: "column",
    alignSelf: "center",
    width: "100%",
    padding: theme.spacing(4),
    gap: theme.spacing(2),
    margin: "auto",
    [theme.breakpoints.up("sm")]: {
        maxWidth: "450px"
    },
    boxShadow:
        "hsla(220, 30%, 5%, 0.05) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.05) 0px 15px 35px -5px",
    ...theme.applyStyles("dark", {
        boxShadow:
            "hsla(220, 30%, 5%, 0.5) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.08) 0px 15px 35px -5px"
    })
}))

const SignInContainer = styled(Stack)(({ theme }) => ({
    minHeight: "100%",
    padding: theme.spacing(2),
    [theme.breakpoints.up("sm")]: {
        padding: theme.spacing(4)
    },
    "&::before": {
        content: '""',
        display: "block",
        position: "absolute",
        zIndex: -1,
        inset: 0,
        backgroundImage:
            "radial-gradient(ellipse at 50% 50%, hsl(210, 100%, 97%), hsl(0, 0%, 100%))",
        backgroundRepeat: "no-repeat",
        ...theme.applyStyles("dark", {
            backgroundImage:
                "radial-gradient(at 50% 50%, hsla(210, 100%, 16%, 0.5), hsl(220, 30%, 5%))"
        })
    }
}))

export default function SignIn(props) {
    const router = useRouter();
    const { data: session, status } = useSession();
    const [error, setError] = React.useState(null)
    const [openToast, setopenToast] = React.useState(false);


    const [loading, setloading] = React.useState(false);

    const [emailError, setEmailError] = React.useState(false)
    const [emailErrorMessage, setEmailErrorMessage] = React.useState("")
    const [passwordError, setPasswordError] = React.useState(false)
    const [passwordErrorMessage, setPasswordErrorMessage] = React.useState("")
    const [open, setOpen] = React.useState(false)

    const handleClickOpen = () => {
        setOpen(true)
    }
 
    const handleClose = () => {
        setOpen(false)
    }

    const githubSignIn = async () => {
        setloading(true)
        await signIn("github", {
            callbackUrl: `${window.location.origin}`,
        })
        setloading(false)
    }
    const handleSubmit = async event => {
        
        let a_validateInputs  = validateInputs();
        if (a_validateInputs) {
            if (emailError || passwordError) {
                event.preventDefault()
                return
            }
            setloading(true)
            const email = document.getElementById("email")
            const password = document.getElementById("password")
            const res = await signIn('credentials', {
                redirect: false,
                username: email.value,
                password: password.value,
                callbackUrl: `${window.location.origin}`,
              })
            setloading(false)
            if (res?.error) {
                console.log('res', res)
                let errX = res.error === 'CredentialsSignin' ? 'Unauthorized !' : res.error;
                setopenToast(true)
                setError(errX)
              } else {
                setError(null)
              }
              if (res.url) router.push(res.url)

        } else {
            return
        }
    }

    const validateInputs = () => {
        const email = document.getElementById("email")
        const password = document.getElementById("password")

        let isValid = true

        if (!email.value || !/\S+@\S+\.\S+/.test(email.value)) {
            setEmailError(true)
            setEmailErrorMessage("Please enter a valid email address.")
            isValid = false
        } else {
            setEmailError(false)
            setEmailErrorMessage("")
        }

        if (!password.value || password.value.length < 6) {
            setPasswordError(true)
            setPasswordErrorMessage("Password must be at least 6 characters long.")
            isValid = false
        } else {
            setPasswordError(false)
            setPasswordErrorMessage("")
        }
        return isValid
    }

    // if (status === "loading") {
    //     return <div>Loading...</div>
    // }

    // if (status === "authenticated") {
    //     router.push('/');
    // }

    const coralBackground = 'https://images.unsplash.com/photo-1461988320302-91bde64fc8e4?ixid=2yJhcHBfaWQiOjEyMDd9'
    return (
        <AppTheme {...props}>
            {/* // <AppTheme theme={defaultTheme}> */}
            <CssBaseline enableColorScheme />
            <Snackbar
                open={openToast}
                autoHideDuration={5000}
                TransitionComponent={SlideTransition}
                onClose={() => setopenToast(false)}
                action={
                    <React.Fragment> 
                        <IconButton
                            aria-label="close"
                            sx={{ p: 0.5 }}
                            onClick={() => setopenToast(false)}
                        >
                            <CloseIcon />
                        </IconButton>
                    </React.Fragment>
                }

                message={<Box>{error}</Box>}
            /> 

            <Box sx={{
                position: 'absolute',
                top: 0, left: 0, right: 0, bottom: 0,
                opacity: 0.10, 
                zIndex: 1,
                }}
                style={{
                    backgroundImage: `url(${coralBackground})`,
                    backgroundSize: 'cover',
                    backgroundRepeat: 'no-repeat',
                    backgroundPosition: 'center',
                    width: '100vw',
                    minHeight: '100vh',
                }}
            >
            </Box>

            {loading && <Box sx={{position: 'absolute', top: 0, left: 0, right: 0}}><LinearProgress /></Box>}
            <SignInContainer direction="column" justifyContent="space-between">
                <Card variant="outlined" sx={{zIndex: 99, alignSelf: 'center', mt: 10}}>
                    {/* <SitemarkIcon /> */}
                    <Typography
                        component="h1"
                        variant="h4"
                        sx={{ width: "100%", fontSize: "clamp(2rem, 10vw, 2.15rem)" }}
                    >
                        Sign in
                    </Typography>
                    <Box
                        component="form"
                        onSubmit={handleSubmit}
                        noValidate
                        sx={{
                            display: "flex",
                            flexDirection: "column",
                            width: "100%",
                            gap: 2
                        }}
                    >
                        <FormControl>
                            <FormLabel htmlFor="email">Email</FormLabel>
                            <TextField
                                error={emailError}
                                helperText={emailErrorMessage}
                                id="email"
                                type="email"
                                name="email"
                                placeholder="<EMAIL>"
                                autoComplete="email"
                                autoFocus
                                required
                                fullWidth
                                variant="outlined"
                                color={emailError ? "error" : "primary"}
                                sx={{ ariaLabel: "email" }}
                            />
                        </FormControl>

                        <FormControl>
                            <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                                <FormLabel htmlFor="password">Password</FormLabel>
                                <Link
                                    component="button"
                                    type="button"
                                    onClick={handleClickOpen}
                                    variant="body2"
                                    sx={{ alignSelf: "baseline" }}
                                >
                                    Forgot your password?
                                </Link>
                            </Box>
                            <TextField
                                error={passwordError}
                                helperText={passwordErrorMessage}
                                name="password"
                                placeholder="••••••"
                                type="password"
                                id="password"
                                autoComplete="current-password"
                                autoFocus
                                required
                                fullWidth
                                variant="outlined"
                                color={passwordError ? "error" : "primary"}
                            />
                        </FormControl>
                        <FormControlLabel
                            control={<Checkbox value="remember" color="primary" />}
                            label="Remember me"
                        />
                        <ForgotPassword open={open} handleClose={handleClose} />
                        <Button
                            // type="submit"
                            fullWidth
                            variant="contained"
                            onClick={handleSubmit}
                        >
                            Sign in
                        </Button>
                        {/* <Typography sx={{ textAlign: "center" }}>
                            Don&apos;t have an account?{" "}
                            <span>
                                <Link
                                    href="/material-ui/getting-started/templates/sign-in/"
                                    variant="body2"
                                    sx={{ alignSelf: "center" }}
                                >
                                    Sign up
                                </Link>
                            </span>
                        </Typography> */}
                    </Box>
                    <Divider>or</Divider>
                    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                        <Button
                            fullWidth
                            variant="outlined"
                            onClick={githubSignIn}
                            startIcon={<GithubIcon />}
                        >
                            Sign in with Github
                        </Button>
                        {/* <Button
                            fullWidth
                            variant="outlined"
                            onClick={() => alert("Sign in with Google")}
                            startIcon={<GoogleIcon />}
                        >
                            Sign in with Google
                        </Button> */}
                    </Box>
                </Card>
            </SignInContainer>

        </AppTheme>
    )
}

function SlideTransition(props) {
    return <Slide {...props} direction="up" />;
  }
  