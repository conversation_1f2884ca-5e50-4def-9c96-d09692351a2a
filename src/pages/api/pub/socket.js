// import { PORT } from "@/config/app"
import { Server } from "socket.io"
const PORT = 4200;
export const config = {
  api: {
    bodyParser: false
  }
}

export default function SocketHandler(_req, res) {
  console.log("Starting Socket.IO server on port:", PORT + 1)
  if (res.socket.server.io) {
    res
      .status(200)
      .json({
        success: true,
        message: "Socket is already running",
        socket: `:${PORT + 1}`
      })
    return
  }

  //@ts-expect-error
  const io = new Server({
    path: "/api/pub/socket",
    addTrailingSlash: false,
    cors: { origin: "*" }
  }).listen(PORT + 1)

  global.io = io;
  io.on("connect", socket => {
    const _socket = socket
    console.log("socket connect", socket.id)
    _socket.broadcast.emit("welcome", `Welcome ${_socket.id}`)
    socket.on("disconnect", async () => {
      console.log("socket disconnect")
    })
  })

  res.socket.server.io = io
  res
    .status(201)
    .json({
      success: true,
      message: "Socket is started",
      socket: `:${PORT + 1}`
    })
}
