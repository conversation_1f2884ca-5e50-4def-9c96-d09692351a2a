
import clientPromise from "@/lib/db/mongodb"
// import sqlClient from '@/lib/db/sqlite';
import { v1 as uuid } from 'uuid';
// const fatura = require("./invoice");
import fatura from '@/lib/fnx/gib.invoice.js'
import moment from "moment";

import {gib, } from '@/lib/fnx/fnx.dbx.js';

const gibTestUsers = [
    '33333301', '33333302', '33333303', '33333304',
    '33333305', '33333306', '33333307', '33333308'
];
const gibTestUser = gibTestUsers[6]

function randomIntFromInterval(min, max) { // min and max included 
    return Math.floor(Math.random() * (max - min + 1) + min);
}

const mainTestMode = true;

export default async function handler(req, res) {
    const reqQuery = req.query;
    const { slug } = req.query
    const section = Array.isArray(slug) ? slug[0] : null
    const dbConn = await clientPromise;
    const db = dbConn.db('marketplace');
    if (section == 'login') { }
    else if (section == 'gibcreate') {
        let dtBOP = Date.now();
        if (req.method !== 'POST') {
            res.status(405).send({ message: 'Only POST requests allowed' })
            return
        }
        try {
            var query = req.query;
            var body = req.body;

            var getResp = {query: query, }
            if (body) {
                let invoiceData = JSON.parse(body);
                let testMode = mainTestMode;
                let data2Process = { ...invoiceData }; 
                let newItems = Array.isArray(data2Process.item) && data2Process.item.map(d => {
                    return ({
                        name: d.name,
                        quantity: parseFloat(d.quantity),
                        unitPrice: parseFloat(parseFloat(d.unitPrice).toFixed(2)),
                        price: parseFloat(parseFloat(d.price).toFixed(2)),
                        VATRate: parseFloat(d.VATRate) * 100,
                        VATAmount: parseFloat(parseFloat(d.VATAmount).toFixed(2)),
                    })
                });
                data2Process.item = newItems;
                data2Process.uuid = uuid();
                delete data2Process.checks;
                delete data2Process.dateCreated;
                delete data2Process.refOrderData;
                delete data2Process.sumDiscountAmount;

                let keysData = data2Process.keysData ? data2Process.keysData : {};
                 
                // console.log('data2Process:', data2Process)
                //for testing for testing 
                if (testMode) {

                    const gibTestUserAct = gibTestUser || gibTestUsers[randomIntFromInterval(0, (gibTestUsers.length - 1))] //'33333307'

                    data2Process.name = "İsim Soyİsim";
                    data2Process.email = '<EMAIL>';
                    data2Process.phoneNumber = '************';
                    data2Process.fullAddress = 'Erenkoy mah. Eren sok. Eren apt. No:23 kat:1 daire:5 Kozyatagi Kadikoy'
                    // data2Process.date = '25-10-2024';
                    keysData.gibName = gibTestUserAct;
                    keysData.gibPass = '1';
                    // data2Process.keysData = keysData;
                }
                let act = await fnCreateGIBInvoice({
                    invoiceData: data2Process,
                    refOrderData: invoiceData.refOrderData,
                    login: {
                        username: keysData.gibName,
                        password: keysData.gibPass,
                    },
                    testMode: testMode,
                })
                // console.log('data2Process Updated:', data2Process, act)
                // let data = preOrderData([dataStg]); 
                getResp.data = invoiceData
                getResp.data2Process = data2Process
                getResp.act = act;
            }

            // var getResp = {query: query, body: body ? JSON.parse(body) : {}}

            res.status(200).json({ ...getResp });
        } catch (e) {
            console.log('errx', e)
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (section == 'gibgetinvoices') {
        let dtBOP = Date.now();
        if (req.method !== 'POST') {
            res.status(405).send({ message: 'Only POST requests allowed' })
            return
        }
        try {
            var query = req.query;
            var body = req.body;
            var getResp = {query: query, }
            if (body) {
                let invoiceData = JSON.parse(body);
                let keysData = invoiceData?.keysData || {};
                let testMode = keysData?.gibTest || mainTestMode;
                
                let startDate = moment().subtract(3650, 'day').format('DD/MM/YYYY');
                let endDate = moment().format('DD/MM/YYYY');

                if (testMode) {
                    const gibTestUserAct = gibTestUser || gibTestUsers[randomIntFromInterval(0, (gibTestUsers.length - 1))] //'33333307'
                    keysData.gibName = '33333307';
                    keysData.gibPass = '1';
                    // data2Process.keysData = keysData;
                }
                let act = await fnGetGIBInvoices({
                    login: {
                        username: keysData.gibName, //AAB03706982
                        password: keysData.gibPass, //BBC853241
                    },
                    startDate, endDate,
                    testMode,
                })
                // console.log('data2Process get:', invoiceData, act)
                // let data = preOrderData([dataStg]); 
                getResp.invoices = invoiceData;
            }

            // var getResp = {query: query, body: body ? JSON.parse(body) : {}}

            res.status(200).json({ ...getResp });
        } catch (e) {
            console.log('errx', e)
            res.status(405).send({ message: 'err check logs' })
            return
        }
    }
    else {
        res.status(405).send({ message: 'No action sent' })
    }
}
 
function createInvoiceInitials(invData, testMode = false) {
    // var uid = uuid();
    // let d = invData.date.toString();
    // let d2b = d.substr(8, 2) + '/' + d.substr(5, 2) + '/' + d.substr(0, 4); //'2024-05-07'
    // console.log('inv Data', invData, d2b)
    let sampleInvoice = invData ? {
        uuid: invData.uuid || uuid(),
        date: moment(invData.date).format('DD/MM/YYYY').toString(),
        time: (invData.time || "12:01"),
        taxIDOrTRID: invData.taxIDOrTRID || '11111111111',
        taxOffice: invData.taxOffice || '',
        title: invData.title || '',
        name: invData.name,
        surname: invData.surname || '',
        fullAddress: invData.fullAddress || '',
        district: invData.district || '',
        city: invData.city || '',
        email: invData.email || '',
        phoneNumber: invData.phoneNumber || '',
        items: invData.item,
        totalVAT: parseFloat(parseFloat(invData.sumKDV).toFixed(2)),
        grandTotal: parseFloat(parseFloat(invData.sumNPrice).toFixed(2)),
        grandTotalInclVAT: parseFloat(parseFloat(invData.sumPrice).toFixed(2)),
        paymentTotal: parseFloat(parseFloat(invData.sumPrice).toFixed(2)),
      } : false

      let testData = {
        date: '25/10/2024',
        time: '15:54:00',
        taxIDOrTRID: '11111111111',
        taxOffice: 'Beyoğlu',
        title: '',
        name: 'XNX SDNSA SS',
        surname: '',
        fullAddress: 'Garibaldi mahallesi AkSaray Arkası sok Taşka apt 22/7',
        district: 'Beyoğlu',
        city: 'İstanbul',
        email: '<EMAIL>',
        phoneNumber: '905003331122',
        items: [
          {
            name: 'Vintage Haç Kolye - KY285',
            quantity: 1,
            unitPrice: 2957.63,
            price: 2957.63,
            VATRate: 18,
            VATAmount: 532.37
          },
          {
            name: 'Cabochon Taşlı Vintage Filigre Broş - BR76',
            quantity: 1,
            unitPrice: 1025.42,
            price: 1025.42,
            VATRate: 18,
            VATAmount: 184.58
          }
        ],
        totalVAT: 716.95,
        grandTotal: 3983.05,
        grandTotalInclVAT: 4700,
        paymentTotal: 4700
      };
    let r = testMode ? testData : sampleInvoice;
    console.log('invoice ref data:', r);
    return r
}

const fnCreateGIBInvoice = async ({invoiceData, refOrderData, login, testMode}) => {
    return new Promise(async (resolve, reject) => {
        let dtBOP = Date.now();
        let result = {};
        testMode && fatura.enableTestMode();
        const token = await fatura.getToken(login.username, login.password);
        if (token?.error) { console.log('login hatası', login) 
            result.code = 0;
            result.err = 'Oturum acilamadi.';
            result.sure = Date.now() - dtBOP;
            console.log('kaydedemedi!')
            resolve({ success: false, ...result, ...token });
        } else {
            let invData = createInvoiceInitials(invoiceData, false ); //testMode
            let postData = {
                orderid: refOrderData.id,
                account: 'shopify-web-shopivo',
                marketID: 1,
                uuid: invData.uuid,
                invoiceStatus: 1, //1 preDraft, //2 draft, //3 signed. //4 sent!
                islemSaati: new Date(),
                invData,
            }
            try {
                const faturaHTML = await fatura.createInvoiceAndGetHTML(
                    login.username,
                    login.password,
                    invData,
                    { sign: false } // Varsayılan olarak sign: true gönderilir.
                )
                if (faturaHTML) {
                    result = {
                        ...result,
                    }
                    result.code = 200;
                    // result.draftInvoice = faturaHTML?.draftInvoice;
                    // result.draftInvoiceDetails = faturaHTML.draftInvoiceDetails;
                    result.sure = Date.now() - dtBOP;
                    console.log('resp', JSON.stringify(result))
                    resolve({ success: true, result, invData, token, faturaHTML  });
                } else {
                    resolve({ success: false, result: false, invData, token  });
                }
            }
            catch(e) {
                result.code = 0;
                result.err = e;
                result.sure = Date.now() - dtBOP;
                console.log('kaydedemedi!')
                resolve({ success: false, result, invData, token  });
            }
        };

    });
}

const fnGetGIBInvoices = async ({ invoiceData, login, testMode, startDate, endDate }) => {
    return new Promise(async (resolve, reject) => {

        startDate = startDate || moment().subtract(45, 'day').format('DD/MM/YYYY');
        endDate = endDate || moment().format('DD/MM/YYYY');
        testMode && fatura.enableTestMode();
        const token = await fatura.getToken(login.username, login.password);
        // console.log('token!', token);
        if (token?.error) {
            console.log(login, token)
            resolve({
                success: false,
                errorDesc: 'login failed!',
                ...token,
            });
        } else {
            console.log('startDate!', { startDate, endDate });
            const invoices = await fatura.getAllInvoicesByDateRange(token, { startDate, endDate })

            resolve({ token, invoices })
        };
    });
}