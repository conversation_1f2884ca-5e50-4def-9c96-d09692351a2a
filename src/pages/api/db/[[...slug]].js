/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

import { decodeJwt } from 'jose';
import {shopify, gib, sqlite_orders, sqlite, app} from '@/lib/fnx/fnx.dbx.js';
import clientPromise from "@/lib/db/mongodb"
// import sqlClient from '@/lib/db/sqlite';

export default async function handler(req, res) {
    const reqQuery = req.query;
    const { slug } = req.query
    const section = Array.isArray(slug) ? slug[0] : null
    const dbConn = await clientPromise;
    const db = dbConn.db('marketplace');
    if (section == 'login') {

    } else if (section == 'createinvoice') {
        if (req.method !== 'POST') {
            res.status(405).send({ message: 'Only POST requests allowed' })
            return
        }
        let dtBOP = Date.now();
        let dt = new Date(dtBOP).toISOString();
        try {
            let resp = await gib.createGIBInvoice({ db, storeName: 'uniqeravintage', modeDebug: true, reqQuery, reqBody: req.body })
            resp.error = false;
            // if(resp?.token) {
            //     await gib.fetchInvoiceHTMLs({
            //         db, storeName: 'uniqeravintage', reqQuery, reqBody: req.body
            //     });
            // }
            console.log(new Date(Date.now()).toISOString(), 'fetchInvoices', Date.now() - dtBOP)
            res.status(200).send({ ...resp })
        } catch (e) {
            res.status(200).send({ error: true, ...e })
        }
    } else if (section == 'fetchinvoices') {

        if (req.method !== 'POST') {
            res.status(405).send({ message: 'Only POST requests allowed' })
            return
        }
        let dtBOP = Date.now();
        let dt = new Date(dtBOP).toISOString();
        try {
            let resp = await gib.fetchInvoices({ db, storeName: 'uniqeravintage', modeDebug: true, reqQuery, reqBody: req.body })
            resp.error = false;
            if(resp?.token) {
                await gib.fetchInvoiceHTMLs({
                    db, storeName: 'uniqeravintage', reqQuery, reqBody: req.body
                });
            }
            console.log(new Date(Date.now()).toISOString(), 'fetchInvoices', Date.now() - dtBOP)
            res.status(200).send({ ...resp })
        } catch (e) {
            res.status(200).send({ error: true, ...e })
        }
    } else if (section == 'fetchinvoicehtmls') {
        if (req.method !== 'POST') {
            res.status(405).send({ message: 'Only POST requests allowed' })
            return
        }
        let dtBOP = Date.now();
        let dt = new Date(dtBOP).toISOString();
        try {
            let resp = await await gib.fetchInvoiceHTMLs({
                db, storeName: 'uniqeravintage', reqQuery, reqBody: req.body
            });
            console.log(new Date(Date.now()).toISOString(), 'fetchinvoicehtmls', Date.now() - dtBOP)
            res.status(200).send({ ...resp })
        } catch (e) {
            res.status(200).send({ error: true, ...e })
        }
    } else if (section == 'sendinvoice') {
        if (req.method !== 'POST') {
            res.status(405).send({ message: 'Only POST requests allowed' })
            return
        }
        let dtBOP = Date.now();
        let dt = new Date(dtBOP).toISOString();
        try {
            let resp = await await gib.sendinvoiceEmail({
                db, storeName: 'uniqeravintage', reqQuery, reqBody: req.body
            });
            console.log(new Date(Date.now()).toISOString(), 'sendinvoice', Date.now() - dtBOP)
            res.status(200).send({ ...resp })
        } catch (e) {
            res.status(200).send({ error: true, ...e })
        }
    } else if (section == 'getinvoicehtml') {
        let dtBOP = Date.now();
        let dt = new Date(dtBOP).toISOString();
        try {
            let resp = await await gib.getInvoiceHTML({
                db, storeName: 'uniqeravintage', reqQuery, 
            });
            console.log(new Date(Date.now()).toISOString(), 'fetchinvoicehtmls', Date.now() - dtBOP)
            res.status(200).send({ ...resp })
        } catch (e) {
            res.status(200).send({ error: true, ...e })
        }
    } else if (section == 'getemailedinvoices') {
        let dtBOP = Date.now();
        let dt = new Date(dtBOP).toISOString();
        try {
            let resp = await await gib.getEmailedInvoices({
                db, storeName: 'uniqeravintage', reqQuery, 
            });
            console.log(new Date(Date.now()).toISOString(), 'fetchinvoicehtmls', Date.now() - dtBOP)
            res.status(200).send({ ...resp })
        } catch (e) {
            res.status(200).send({ error: true, ...e })
        }
    } else if (section == 'listinvoicescloud') {
        let dtBOP = Date.now();
        let dt = new Date(dtBOP).toISOString();
        try {
            console.log(new Date(Date.now()).toISOString(), 'listinvoicescloud', Date.now() - dtBOP)
            var getResp = await gib._listInvoices({
                db,
                storeName: 'uniqeravintage', modeDebug: false,
                reqQuery: req.query,
            })
            res.status(200).json({ ...getResp, sure: Date.now() - dtBOP })
        } catch (e) {
            console.log('errx', e)
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (section == 'listinvoicescloudone') {
        let dtBOP = Date.now();
        let dt = new Date(dtBOP).toISOString();
        try {
            console.log(new Date(Date.now()).toISOString(), 'listinvoicescloudone', Date.now() - dtBOP)
            var getResp = await gib._listInvoice({
                db,
                storeName: 'uniqeravintage', modeDebug: false,
                reqQuery: req.query,
            })
            res.status(200).json({ ...getResp, sure: Date.now() - dtBOP })
        } catch (e) {
            console.log('errx', e)
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (section == 'listorderscloudone') {
        let dtBOP = Date.now();
        let dt = new Date(dtBOP).toISOString();
        try {
            console.log(new Date(Date.now()).toISOString(), 'listorderscloudone', Date.now() - dtBOP)
            var getResp = await shopify._listOrder({
                db,
                storeName: 'uniqeravintage', modeDebug: false,
                reqQuery: req.query,
            })
            res.status(200).json({ ...getResp, sure: Date.now() - dtBOP })
        } catch (e) {
            console.log('errx', e)
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (section == 'setdb') {
        let dtBOP = Date.now();
        try {
            var getResp = await sqlite.setSqlDB({
                db,
                // sqlClient,
                // userInitials,
                storeName: 'uniqeravintage', modeDebug: true,
                reqQuery: req.query,
            });
            res.status(200).json({ ...getResp });
        } catch (e) {
            console.log('errx', e)
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (section == 'fetchorders') {
        let dtBOP = Date.now();
        let dt = new Date(dtBOP).toISOString();
        await shopify.fetchOrders({db, storeName: 'uniqeravintage', modeDebug: true, reqQuery })
        console.log(new Date(Date.now()).toISOString(), 'fetchorders', Date.now() - dtBOP)
        res.status(200).send({ message: dt })
    } else if (section == 'listorders') {
        let dtBOP = Date.now();
        let dt = new Date(dtBOP).toISOString();
        try {
            // const userInitials = getTokenData(req); //{id, email, phone, fullName, role}
            console.log(new Date(Date.now()).toISOString(), 'listorders', Date.now() - dtBOP)
            var getResp = await sqlite_orders.listorders({
                // db: sqlClient,
            })
            res.status(200).json({ ...getResp, sure: Date.now() - dtBOP })
        } catch (e) {
            console.log('errx', e)
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (section == 'listorderscloud') {
        let dtBOP = Date.now();
        let dt = new Date(dtBOP).toISOString();
        try {
            console.log(new Date(Date.now()).toISOString(), 'listorders', Date.now() - dtBOP)
            var getResp = await shopify._listOrders({
                db,
                storeName: 'uniqeravintage', modeDebug: false,
                reqQuery: req.query,
            })
            res.status(200).json({ ...getResp, sure: Date.now() - dtBOP })
        } catch (e) {
            console.log('errx', e)
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (section == 'getorder') {
        let dtBOP = Date.now();
        let dt = new Date(dtBOP).toISOString();
        try {
            const userInitials = getTokenData(req); //{id, email, phone, fullName, role}
            console.log(new Date(Date.now()).toISOString(), 'listorders', Date.now() - dtBOP)
            var getResp = await shopify._getOrder({
                db,
                userInitials,
                storeName: 'uniqeravintage', modeDebug: true,
                reqQuery: req.query,
            })
            res.status(200).json({ ...getResp })
        } catch (e) {
            console.log('errx', e)
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (section == 'getsettings') {
        let dtBOP = Date.now();
        let dt = new Date(dtBOP).toISOString();
        try {
            const userInitials = getTokenData(req); //{id, email, phone, fullName, role}
            console.log(new Date(Date.now()).toISOString(), 'getsettings', Date.now() - dtBOP)
            var getResp = await app.getSettings({
                db,
                userInitials,
                storeName: 'uniqeravintage', modeDebug: true,
                reqQuery: req.query,
            })
            res.status(200).json({ ...getResp })
        } catch (e) {
            console.log('errx', e)
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } else if (section == 'setsettings') {
        let dtBOP = Date.now();
        let dt = new Date(dtBOP).toISOString();
        try {

            if (req.method !== 'POST') {
                res.status(405).send({ message: 'Only POST requests allowed' })
                return
            }

            const userInitials = getTokenData(req); //{id, email, phone, fullName, role}
            console.log(new Date(Date.now()).toISOString(), 'getsettings', Date.now() - dtBOP)
            var getResp = await app.setSettings({
                db,
                userInitials,
                storeName: 'uniqeravintage', modeDebug: true,
                reqQuery: req.query, reqBody: req.body
            })
            res.status(200).json({ ...getResp })
        } catch (e) {
            console.log('errx', e)
            res.status(405).send({ message: 'err check logs' })
            return
        }
    } 
    //
    else {
        res.status(405).send({ message: 'No action sent' })
    }
}


const getTokenData = req => {
    var hasMappedHeaders = function (headers) {
        return headers instanceof Headers;
    };
    try {
        const jwt = hasMappedHeaders(req?.headers)
            ? req.headers.get('authorization')
            : req.headers['authorization'];
        const bearerToken = jwt ? jwt.split(" ") : [];
        const token = bearerToken[1];
        var decodedJWT = decodeJwt(token)
        const { id, email, phone, fullName, role, refObject} = decodedJWT ? decodedJWT : {}
        return { id, email, phone, fullName, role, token, refObject }
    } catch (e) {
        console.log('getTokenData e', e)
        return {}
    }
}