import React, { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/router';
import { Box, Card, CardContent, CircularProgress, Typography, CardHeader, IconButton, Collapse, Checkbox } from '@mui/material';
import ResetIcon from '@mui/icons-material/RotateLeft';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import moment from "moment";
import localforage from 'localforage';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import useMediaQuery from '@mui/material/useMediaQuery';
import { styled, useTheme } from '@mui/material/styles';
import Grid from '@mui/material/Grid2';
import FormControl from "@mui/material/FormControl"
import FormLabel from "@mui/material/FormLabel"
import TextField from "@mui/material/TextField"
import Stack from "@mui/material/Stack"
import MuiCard from "@mui/material/Card"
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import AddBoxIcon from '@mui/icons-material/AddBox';
import LoadingButton from "@mui/lab/LoadingButton"
import {
    Table,
    TableHead,
    TableBody,
    TableRow,
    TableCell,
    TableContainer,
    Paper,
    Chip,
    Tooltip
} from '@mui/material';
import { titlecase, camalize } from '@/lib/fnx/fnx.cli'

const OrdersExcel = () => {
  const router = useRouter();
  const { selectedOrders } = router.query;
  const [orderDetails, setOrderDetails] = useState([]);
  const [loading, setLoading] = useState(false);
  const [excelData, setExcelData] = useState({});
  const [selectedInvoices, setSelectedInvoices] = useState({});

  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (selectedOrders) {
        setLoading(true);
        try {
          const orderIds = JSON.parse(selectedOrders).map(order => order.orderID);
          const fetchedDetails = await Promise.all(
            orderIds.map(async (orderID) => {
                console.log(`Fetching details for order ID: ${orderID}`);
              const response = await fetch(`/api/db/getorder/${orderID}`);
              const data = await response.json();
              return data;
            })
          );
          setOrderDetails(fetchedDetails);
        } catch (error) {
          console.error("Error fetching order details:", error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchOrderDetails();
  }, [selectedOrders]);

  return (
    <div>
      {loading && <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}><CircularProgress /></Box>}
      {!loading && orderDetails.length > 0 ? (
        orderDetails.map((order, index) => (
            <FaturaCard 
                data={order.data} 
                key={order.data.id}
                onInvoiceDataChange={(orderId, data) => {
                    const newData = {
                        ...excelData,
                        [orderId]: data
                    };
                    setExcelData(newData);
                }}
                isSelected={selectedInvoices[order.data.id]}
                onSelectionChange={(orderId, isChecked) => {
                    const newSelected = {
                        ...selectedInvoices,
                        [orderId]: isChecked
                    };
                    setSelectedInvoices(newSelected);
                }}
            />
         ))
      ) : (
        !loading && <p>No order details available.</p>
      )}
      <div style={{ display: 'flex', justifyContent: 'center', marginTop: '20px' }}>
        <Button 
          variant="contained" 
          color="primary" 
          onClick={() => {
            const selectedData = Object.entries(excelData)
              .filter(([orderId]) => selectedInvoices[orderId])
              .map(([_, data]) => data);
            console.log('Selected Invoice Data:', selectedData);
          }}
        >
          export 2 excel
        </Button>
      </div>
    </div>
  );
};


const prepRefOrderData = orderData => {
    const { id, checkout_id, closed_at, confirmation_number, created_at, currency,
        current_total_price, current_total_tax, name, number, order_number, order_status_url,
        processed_at, reference, token, total_price, updated_at,
    } = orderData;

    return ({
        id, checkout_id, closed_at, confirmation_number, created_at, currency,
        current_total_price, current_total_tax, name, number, order_number, order_status_url,
        processed_at, reference, token, total_price, updated_at
    })
}
const prepInvoiceData = orderData => {
    let resp = {}
    let rndID = (+new Date * Math.random()).toString(36).substring(0, 6);
    resp.id = 'inv_' + rndID;
    resp.dateCreated = new Date(Date.now()).toISOString();
    resp.refOrderData = prepRefOrderData(orderData);
    resp.name = titlecase(orderData?.billing_address?.name);
    resp.fullAddress = titlecase(orderData?.billing_address?.address1) + (orderData?.billing_address?.address2 ? ' ' + titlecase(orderData?.billing_address?.address2) : '');
    resp.district = titlecase(orderData?.billing_address?.province);
    resp.city = orderData?.billing_address?.city ? titlecase(orderData?.billing_address?.city) : '';
    resp.email = orderData?.customer?.email || '';
    resp.phoneNumber = orderData?.customer?.phone || '';

    resp.date = orderData?.created_at ? moment(orderData?.created_at).format('yyyy-MM-DD') : "";
    resp.time = orderData?.created_at ? orderData?.created_at.substr(11, 5) : '';
    resp.taxIDOrTRID = '11111111111';
    resp.taxOffice = orderData?.billing_address?.city ? titlecase(orderData?.billing_address?.city) : '';
    var itemx = [];
    Array.isArray(orderData?.line_items) && orderData?.line_items.map((item, ix) => {
        let randStr = (+new Date * Math.random()).toString(36).substring(0, 6);
        let taxRate = item.tax_lines && Array.isArray(item.tax_lines) && item.tax_lines[0]?.rate && parseFloat(item.tax_lines[0]?.rate);
        let discAmount = item.discount_allocations && Array.isArray(item.discount_allocations) && item.discount_allocations[0]?.amount;

        let urunSatisFiyati = parseFloat(item.price)
        let urunFiyati = item.price && parseFloat(urunSatisFiyati - (discAmount || 0))
        let urunFiyatiKDVsiz = urunFiyati / (1 + taxRate);
        let urunKDVTutari = urunFiyatiKDVsiz * taxRate
        let urunAdedi = item.current_quantity;
        let satirToplamKDVDahilUrunFiyati = urunFiyati * urunAdedi;
        let satirToplamKDVHaricUrunFiyati = urunFiyatiKDVsiz * urunAdedi;
        let satirToplamKDVTutari = urunKDVTutari * urunAdedi

        let itemPrice = item.price && parseFloat(urunSatisFiyati - (discAmount || 0)).toFixed(2);
        let itemTax = item.tax_lines && Array.isArray(item.tax_lines) && parseFloat(item.tax_lines && Array.isArray(item.tax_lines) && item.tax_lines[0]?.price).toFixed(2);
        let itemPriceWOTax = itemPrice / (1 + taxRate); //parseFloat(itemPrice - itemTax).toFixed(2)

        itemx.push({
            id: randStr + '_' + (ix + 1),
            name: titlecase(item.name),
            quantity: urunAdedi,
            urunSatisFiyati,
            discAmount,
            urunFiyati,
            urunFiyatiKDVsiz,
            taxRate,
            urunKDVTutari,
            satirToplamKDVDahilUrunFiyati,
            satirToplamKDVHaricUrunFiyati,
            satirToplamKDVTutari,
        });
    });

    let sumPrice = itemx.reduce((acc, x) => acc + (parseFloat(x.urunFiyati) * x.quantity), 0);
    let sumNPrice = itemx.reduce((acc, x) => acc + (parseFloat(x.urunFiyatiKDVsiz) * x.quantity), 0);
    let sumKDV = itemx.reduce((acc, x) => acc + (parseFloat(x.urunKDVTutari) * x.quantity), 0);
    let discAmounts = itemx.reduce((acc, x) => acc + (parseFloat(x.discAmount) * x.quantity), 0);

    resp.item = itemx;
    resp.sumDiscountAmount = discAmounts;
    resp.sumPrice = sumPrice;
    resp.sumNPrice = sumNPrice;
    resp.sumKDV = sumKDV;
    resp.checks = [
        { checkName: 'total_discounts', result: discAmounts == orderData?.total_discounts },
        { checkName: 'sumPrice', result: sumPrice == orderData?.total_price },
        { checkName: 'sumKDV', result: sumKDV == orderData?.total_tax },
    ]
    return (resp)

}

const FaturaCard = props => {
    const { data, onInvoiceDataChange, onSelectionChange, isSelected } = props;

    const theme = useTheme();

    const [orderData, setorderData] = useState(props.data ? props.data : false);
    const [invoiceData, setinvoiceDataS] = React.useState(false);
    const [invoiceDataInitial, setinvoiceDataInitial] = React.useState(false);
    
    useEffect(() => {
        if (props.data) {
            setorderData(props.data)
            let invData = prepInvoiceData(props.data)
            setinvoiceDataR(invData);
            setinvoiceDataInitial(invData);
        }
    }, [props.data])

    const setinvoiceDataR = invData => {
        setinvoiceDataS(invData);
        onInvoiceDataChange(orderData.id, invData);
    }  
    const setinvoiceData = (field, value) => {
        setinvoiceDataS(prevData => {
            const newData = {
                ...prevData,
                [field]: value
            };
            onInvoiceDataChange(orderData.id, newData);
            return newData;
        });
    }
    const handleLineItemChange = (elementIndex, event, fieldName) => {
        setinvoiceDataS(prevData => {
            const invoiceDataStg = { ...prevData };
            const lineItems = [...(invoiceDataStg.item || [])];
            
            lineItems[elementIndex] = {
                ...lineItems[elementIndex],
                [event.target.name]: event.target.value,
            };

            if (event.target.name === 'taxRate') {
                lineItems[elementIndex].taxRate = parseFloat(event.target.value) / 100;
            }

            const item = lineItems[elementIndex];
            const urunFiyatiKDVsiz = parseFloat(item.urunFiyati) / (1 + parseFloat(item.taxRate));
            const urunKDVTutari = urunFiyatiKDVsiz * parseFloat(item.taxRate);

            lineItems[elementIndex] = {
                ...item,
                urunFiyatiKDVsiz,
                urunKDVTutari,
                urunSatisFiyati: parseFloat(item.urunFiyati) + (item.discAmount ? parseFloat(item.discAmount) : 0),
                satirToplamKDVDahilUrunFiyati: parseFloat(item.urunFiyati) * parseFloat(item.quantity),
                satirToplamKDVHaricUrunFiyati: urunFiyatiKDVsiz * parseFloat(item.quantity),
                satirToplamKDVTutari: urunKDVTutari * parseFloat(item.quantity)
            };

            const newData = {
                ...invoiceDataStg,
                item: lineItems,
                sumPrice: lineItems.reduce((acc, x) => acc + (parseFloat(x.urunFiyati) * x.quantity), 0),
                sumNPrice: lineItems.reduce((acc, x) => acc + (parseFloat(x.urunFiyatiKDVsiz) * x.quantity), 0),
                sumKDV: lineItems.reduce((acc, x) => acc + (parseFloat(x.urunKDVTutari) * x.quantity), 0),
                sumDiscountAmount: lineItems.reduce((acc, x) => acc + (x.discAmount ? parseFloat(x.discAmount) : 0) * x.quantity, 0)
            };

            onInvoiceDataChange(orderData.id, newData);
            return newData;
        });
    }
    const handleRemoveLineItem = (elementIndex, line) => {
        let invoiceDataStg = invoiceData
        let invoiceLines = invoiceDataStg.item;
        let lineItems = invoiceLines.filter((item, i) => {
            return elementIndex !== i
        })
        invoiceDataStg.item = lineItems;
        setinvoiceDataR(JSON.parse(JSON.stringify(invoiceDataStg)))
    }
    const [expanded, setExpanded] = React.useState(false);

    const handleExpandClick = () => {
        setExpanded(!expanded);
    };

    return (
        <Card sx={{ mb: 2, m: 4, p: 0, shadow: 3, borderRadius: 2, }}>
            <CardHeader
                subheader={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Checkbox
                            checked={isSelected}
                            onChange={(e) => onSelectionChange(orderData.id, e.target.checked)}
                        />
                        <Typography>{orderData.order_number} - {orderData?.customer?.first_name} {orderData?.customer?.last_name} - {orderData.total_price} {orderData.currency}`</Typography>
                    </Box>
                    }
                action={
                    <IconButton
                        onClick={handleExpandClick}
                        aria-expanded={expanded}
                        aria-label="show more"
                    >
                        {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                }
            />
            <Collapse in={expanded} timeout="auto" >
                <CardContent>
                    {orderData && <InvoiceHeader 
                        data={orderData}
                        invoiceData={invoiceData} 
                        setinvoiceData={setinvoiceData} 
                    />}

                    {orderData && <InvoiceItems 
                        data={orderData}
                        invoiceData={invoiceData}
                        handleLineItemChange={handleLineItemChange}
                        handleRemoveLineItem={handleRemoveLineItem}
                    />}
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                        <IconButton
                            onClick={() => {
                                const initialData = JSON.parse(JSON.stringify(invoiceDataInitial));
                                setinvoiceDataS(initialData);
                            }}
                            aria-label="reset"
                        >
                            <ResetIcon />
                        </IconButton>
                    </Box>
                    {/* Display other order details here */}
                    {/* <pre style={{ whiteSpace: 'pre-wrap', fontSize: 10 }}>{JSON.stringify(orderData, null, 2)}</pre> */}
                    {/* <pre style={{ whiteSpace: 'pre-wrap', fontSize: 10 }}>{JSON.stringify(invoiceData, null, 2)}</pre> */}
                </CardContent>
            </Collapse>
        </Card>
    );
}



const InvoiceHeader = props => {
    const { data, invoiceData, setinvoiceData } = props;
    const [orderData, setorderData] = useState(data);
    let refDistrict = useRef(null);

    useEffect(() => {
        setorderData(data);
    }, [data]);

    // const handleChange = (field, fvalue) => {
    //     let currFormV = JSON.parse(JSON.stringify(invoiceData));
    //     currFormV[field] = fvalue?.nativeEvent?.target.value;
    //     setinvoiceData(currFormV);
    // }

    const handleChange = (field, event) => {
        setinvoiceData(field, event.target.value);
    }
    const [faturaTarihi, setFaturaTarihi] = React.useState(null);

    const [emailError, setEmailError] = React.useState(false)
    const [emailErrorMessage, setEmailErrorMessage] = React.useState("")

    const [nameError, setnameError] = React.useState(false)
    const [nameErrorMessage, setnameErrorMessage] = React.useState("")

    const [addressError, setaddressError] = React.useState(false)
    const [addressErrorMessage, setaddressErrorMessage] = React.useState("")

    const [districtError, setdistrictError] = React.useState(false)
    const [districtErrorMessage, setdistrictErrorMessage] = React.useState("")

    const [cityError, setcityError] = React.useState(false)
    const [cityErrorMessage, setcityErrorMessage] = React.useState("")

    const [phoneNumberError, setphoneNumberError] = React.useState(false)
    const [phoneNumberErrorrMessage, setphoneNumberErrorMessage] = React.useState("")

    const [dateError, setdateError] = React.useState(false)
    const [dateErrorMessage, setdateErrorMessage] = React.useState("")

    const [timeError, settimeError] = React.useState(false)
    const [timeErrorMessage, settimeErrorMessage] = React.useState("")

    const [taxOfficeError, settaxOfficeError] = React.useState(false)
    const [taxOfficeErrorMessage, settaxOfficeErrorMessage] = React.useState("")

    const [taxIDOrTRIDError, settaxIDOrTRIDError] = React.useState(false)
    const [taxIDOrTRIDErrorMessage, settaxIDOrTRIDErrorMessage] = React.useState("")

    const setFaturaTarihiR = trh => {
        setFaturaTarihi(trh)
    }

    return (
        <>
            <FormControl>
                <Grid
                    container
                    spacing={2}
                    columns={12}
                    sx={{ mb: theme => theme.spacing(1), pb: 2, borderBottomWidth: 1, borderBottomColor: '#333' }}
                >
                    <Grid size={{ xs: 12, sm: 6, lg: 6 }}>
                        {/* <HighlightedCard /> */}
                        <Box
                            sx={{
                                display: "flex",
                                flexDirection: "column",
                                width: "100%",
                                gap: 2
                            }}>
                            <FormControl>
                                <FormLabel htmlFor="name" sx={{ p: 0, m: 0 }}>Müşteri Adı</FormLabel>
                                <TextField
                                    error={nameError}
                                    helperText={nameErrorMessage}
                                    id="name"
                                    type="text"
                                    name="name"
                                    placeholder="Müşteri adı"
                                    autoComplete="name"
                                    autoFocus
                                    required
                                    fullWidth
                                    variant="outlined"
                                    color={nameError ? "error" : "primary"}
                                    value={invoiceData?.name || ''}
                                    // defaultValue={invoiceData?.name ||  ''}
                                    sx={{ ariaLabel: "name" }}
                                    onChange={(e) => handleChange('name', e)}
                                />
                            </FormControl>

                            <FormControl>
                                <FormLabel htmlFor="fullAddress" sx={{ p: 0, m: 0 }}>Fatura Adresi</FormLabel>
                                <TextField
                                    error={addressError}
                                    size="small"
                                    helperText={addressErrorMessage}
                                    id="fullAddress"
                                    type="text"
                                    name="fullAddress"
                                    placeholder="fatura adresi"
                                    autoComplete="address"
                                    autoFocus
                                    required
                                    fullWidth
                                    variant="outlined"
                                    // multiline
                                    maxRows={2}
                                    value={invoiceData?.fullAddress || ''}
                                    // defaultValue={invoiceData?.fullAddress ||  ''}
                                    color={addressError ? "error" : "primary"}
                                    sx={{ ariaLabel: "fullAddress", }}
                                    onChange={(e) => handleChange('fullAddress', e)}
                                />
                            </FormControl>

                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "row",
                                    width: "100%",
                                    gap: 2
                                }}>
                                <FormControl>
                                    <TextField
                                        ref={refDistrict}
                                        error={districtError}
                                        size="small"
                                        helperText={districtErrorMessage}
                                        id="district"
                                        type="text"
                                        name="district"
                                        placeholder="fatura adresi ilce"
                                        autoComplete="district"
                                        autoFocus
                                        required
                                        fullWidth
                                        variant="outlined"
                                        value={invoiceData?.district || ''}
                                        // defaultValue={invoiceData.district}
                                        color={districtError ? "error" : "primary"}
                                        sx={{ ariaLabel: "district", }}
                                        onChange={(e) => handleChange('district', e)}
                                    />
                                </FormControl>
                                <FormControl>
                                    {/* <FormLabel htmlFor="city">Il</FormLabel> */}
                                    <TextField
                                        error={cityError}
                                        size="small"
                                        helperText={cityErrorMessage}
                                        id="city"
                                        type="text"
                                        name="city"
                                        placeholder="fatura adresi ili"
                                        autoComplete="city"
                                        autoFocus
                                        required
                                        fullWidth
                                        variant="outlined"
                                        value={invoiceData.city || ''}
                                        // defaultValue={invoiceData.city ||  ''}
                                        color={cityError ? "error" : "primary"}
                                        sx={{ ariaLabel: "city", }}
                                        onChange={(e) => handleChange('city', e)}
                                    />
                                </FormControl>

                            </Box>
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "row",
                                    width: "100%",
                                    gap: 2
                                }}>

                                <FormControl>
                                    <FormLabel htmlFor="taxOffice" sx={{ p: 0, m: 0 }}>Vergi Dairesi</FormLabel>
                                    <TextField
                                        error={taxOfficeError}
                                        helperText={taxOfficeErrorMessage}
                                        id="taxOffice"
                                        type="text"
                                        name="taxOffice"
                                        placeholder="555xxxx"
                                        autoComplete="taxOffice"
                                        autoFocus
                                        required
                                        fullWidth
                                        variant="outlined"
                                        value={invoiceData?.taxOffice || ''}
                                        // defaultValue={invoiceData?.taxOffice ||  ''}
                                        onChange={(e) => handleChange('taxOffice', e)}
                                        color={taxOfficeError ? "error" : "primary"}
                                        sx={{ ariaLabel: "taxOffice" }}
                                    />
                                </FormControl>

                                <FormControl>
                                    <FormLabel htmlFor="taxIDOrTRID" sx={{ p: 0, m: 0 }}>Vergi D. No</FormLabel>
                                    <TextField
                                        error={taxIDOrTRIDError}
                                        helperText={taxIDOrTRIDErrorMessage}
                                        id="taxIDOrTRID"
                                        type="text"
                                        name="taxIDOrTRID"
                                        placeholder="555xxxx"
                                        autoComplete="taxIDOrTRID"
                                        autoFocus
                                        required
                                        fullWidth
                                        variant="outlined"
                                        value={invoiceData?.taxIDOrTRID || ''}
                                        // defaultValue={invoiceData?.taxIDOrTRID ||  ''}
                                        color={taxIDOrTRIDError ? "error" : "primary"}
                                        onChange={(e) => handleChange('taxIDOrTRID', e)}
                                        sx={{ ariaLabel: "taxIDOrTRID" }}
                                    />
                                </FormControl>
                            </Box>
                        </Box>
                    </Grid>
                    <Grid size={{ xs: 12, sm: 3, lg: 3 }}>
                        {/* <HighlightedCard /> */}
                        <Box
                            sx={{
                                display: "flex",
                                flexDirection: "column",
                                width: "100%",
                                gap: 2
                            }}>
                            <FormControl>
                                <FormLabel htmlFor="email" sx={{ p: 0, m: 0 }}>Eposta</FormLabel>
                                <TextField
                                    error={emailError}
                                    helperText={emailErrorMessage}
                                    id="email"
                                    type="email"
                                    name="email"
                                    placeholder="<EMAIL>"
                                    autoComplete="email"
                                    autoFocus
                                    required
                                    fullWidth
                                    value={invoiceData?.email || ''}
                                    // defaultValue={invoiceData?.email || ''}
                                    variant="outlined"
                                    onChange={(e) => handleChange('email', e)}
                                    color={emailError ? "error" : "primary"}
                                    sx={{ ariaLabel: "email", }}
                                />
                            </FormControl>
                            <FormControl>
                                <FormLabel htmlFor="phoneNumber" sx={{ p: 0, m: 0 }}>Telefon No</FormLabel>
                                <TextField
                                    error={phoneNumberError}
                                    helperText={phoneNumberErrorrMessage}
                                    id="phoneNumber"
                                    type="phoneNumber"
                                    name="phoneNumber"
                                    placeholder="555xxxx"
                                    autoComplete="phoneNumber"
                                    autoFocus
                                    required
                                    fullWidth
                                    variant="outlined"
                                    value={invoiceData?.phoneNumber || ''}
                                    // defaultValue={invoiceData?.phoneNumber || ''}
                                    onChange={(e) => handleChange('phoneNumber', e)}
                                    color={phoneNumberError ? "error" : "primary"}
                                    sx={{ ariaLabel: "phoneNumber" }}
                                />
                            </FormControl>
                            <Grid
                                container
                                spacing={2}
                                columns={12}
                                sx={{ mb: theme => theme.spacing(2) }}
                            >
                                <Grid size={{ xs: 12, sm: 6, lg: 6 }}>

                                    <FormLabel htmlFor="date" sx={{ p: 0, m: 0 }}>Fatura Tarihi</FormLabel>
                                    {/* {JSON.stringify(orderData?.created_at ? moment(orderData?.created_at).format('yyyy-MM-DD') : "")} */}
                                    <TextField
                                        id="date"
                                        name="date"
                                        type="date"
                                        error={dateError}
                                        color={dateError ? "error" : "primary"}
                                        helperText={dateErrorMessage}
                                        placeholder={new Date(Date.now()).toISOString()}
                                        value={moment(invoiceData?.date).format('yyyy-MM-DD') || ''}
                                        // defaultValue={moment(invoiceData?.date).format('yyyy-MM-DD') || ''}
                                        onChange={(e) => handleChange('date', e)}
                                    // onChange={e => setFaturaTarihiR(e)}
                                    />

                                </Grid>
                                <Grid size={{ xs: 12, sm: 6, lg: 6 }}>
                                    <FormLabel htmlFor="time">Saat</FormLabel>
                                    {/* {orderData?.created_at.substring(16, 11)} */}
                                    <TextField
                                        error={timeError}
                                        helperText={timeErrorMessage}
                                        id="time"
                                        type="time"
                                        name="time"
                                        placeholder="12:01"
                                        autoComplete="time"
                                        autoFocus
                                        required
                                        fullWidth
                                        value={invoiceData?.time || ''}
                                        // defaultValue={invoiceData?.time || ''}
                                        onChange={(e) => handleChange('time', e)}
                                        variant="outlined"
                                        color={timeError ? "error" : "primary"}
                                        sx={{ ariaLabel: "time" }}
                                    />
                                </Grid>
                            </Grid>
                        </Box>
                    </Grid>

                    <Grid size={{ xs: 12, sm: 3, lg: 3 }}>
                        {/* <HighlightedCard /> */}
                        <Box
                            sx={{
                                display: "flex",
                                flexDirection: "column",
                                width: "100%",
                                gap: 2,
                                maxHeight: '250px',
                                overflowY: 'auto',
                            }}>

                            <Stack sx={{ flexDirection: 'row', alignItems: 'center', py: 1 }}>
                                <Box sx={{ flex: 3, textAlign: 'right' }}>
                                    Ara Toplam:
                                </Box>
                                <Box sx={{ flex: 1, textAlign: 'right', px: 3 }}>

                                    {invoiceData?.sumDiscountAmount && invoiceData?.sumPrice && (parseFloat(invoiceData?.sumPrice) + parseFloat(invoiceData?.sumDiscountAmount)).toFixed(2)}
                                </Box>
                            </Stack>
                            <Stack sx={{ flexDirection: 'row', alignItems: 'center', py: 1 }}>
                                <Box sx={{ flex: 3, textAlign: 'right' }}>
                                    İndirimler Toplam:
                                </Box>
                                <Box sx={{ flex: 1, textAlign: 'right', px: 3 }}>
                                    {!isNaN(invoiceData?.sumDiscountAmount) ? parseFloat(invoiceData?.sumDiscountAmount).toFixed(2) : '0.00'}
                                </Box>
                            </Stack>
                            <Stack sx={{ flexDirection: 'row', alignItems: 'center', py: 1 }}>
                                <Box sx={{ flex: 3, textAlign: 'right' }}>
                                    KDV Hariç :
                                </Box>
                                <Box sx={{ flex: 1, textAlign: 'right', px: 3 }}>
                                    {invoiceData?.sumNPrice && parseFloat(invoiceData?.sumNPrice).toFixed(2)}
                                </Box>
                            </Stack>
                            <Stack sx={{ flexDirection: 'row', alignItems: 'center', py: 1 }}>
                                <Box sx={{ flex: 3, textAlign: 'right' }}>
                                    KDV :
                                </Box>
                                <Box sx={{ flex: 1, textAlign: 'right', px: 3 }}>
                                    {invoiceData?.sumKDV && parseFloat(invoiceData?.sumKDV).toFixed(2)}
                                </Box>
                            </Stack>
                            <Stack sx={{ flexDirection: 'row', alignItems: 'center', py: 1 }}>
                                <Box sx={{ flex: 3, textAlign: 'right' }}>
                                    Genel Toplam:
                                </Box>
                                <Box sx={{ flex: 1, textAlign: 'right', px: 3 }}>
                                    {invoiceData?.sumPrice && parseFloat(invoiceData?.sumPrice).toFixed(2)}
                                </Box>
                            </Stack>

                            {/* <pre style={{ whiteSpace: 'pre-wrap', fontSize: 10 }}>{JSON.stringify(invoiceData, null, 2)}</pre> */}
                        </Box>
                    </Grid>

                </Grid>
            </FormControl>
        </>
    )
}


const InvoiceItems = props => {
    const { data, invoiceData, handleLineItemChange, handleRemoveLineItem } = props;
    const [orderData, setorderData] = useState(data);
    const [invoiceSumData, setinvoiceSumData] = useState({
        sumPriceBeforeDisc: 0,
        sumPrice: 0,
        sumNPrice: 0,
        sumKDV: 0,
        discAmounts: 0
    });

    useEffect(() => {
        setorderData(data);
    }, [data]);

    useEffect(() => {
        if (invoiceData.item && Array.isArray(invoiceData.item)) {
            let sumPrice = invoiceData.item.reduce((acc, x) => acc + parseFloat(x.satirToplamKDVDahilUrunFiyati), 0);
            let sumNPrice = invoiceData.item.reduce((acc, x) => acc + parseFloat(x.satirToplamKDVHaricUrunFiyati), 0);
            let sumKDV = invoiceData.item.reduce((acc, x) => acc + parseFloat(x.satirToplamKDVTutari), 0);
            let discAmounts = invoiceData.item.reduce((acc, x) => acc + (x.discAmount ? parseFloat(x.discAmount) : 0), 0);
            let sumPriceBeforeDisc = invoiceData.item.reduce((acc, x) => acc + (parseFloat(x.urunSatisFiyati) * x.quantity), 0);
            setinvoiceSumData({
                sumPriceBeforeDisc,
                sumPrice,
                sumNPrice,
                sumKDV,
                discAmounts,
            })
        }
    }, [invoiceData])

    return (
        <>
            <Typography variant="body1" sx={{ fontWeight: '600' }}>
                Fatura Kalemleri
            </Typography>
            <Grid
                container
                spacing={2}
                columns={12}
                sx={{ mb: theme => theme.spacing(1), pb: 1, borderBottomWidth: 1, borderBottomColor: '#333' }}
            >
                <Grid size={{ xs: 12, sm: 5, lg: 5 }}>
                    Ürün
                </Grid>
                <Grid size={{ xs: 12, sm: 1, lg: 1 }} textAlign={'center'}>
                    Adet
                </Grid>

                <Grid size={{ xs: 12, sm: 2, lg: 2 }} textAlign={'center'}>
                    kdv oran
                </Grid>

                <Grid size={{ xs: 12, sm: 1.5, lg: 1.5 }} textAlign={'center'} title={'kdv dahil '}>
                    urun fiyati*
                </Grid>

                <Grid size={{ xs: 12, sm: 1.5, lg: 1.5 }} textAlign={'center'} title={'kdv dahil '}>
                    toplam tutar*
                </Grid>

                <Grid size={{ xs: 12, sm: 1, lg: 1 }} textAlign={'center'}>
                    &nbsp;
                </Grid>

            </Grid>

            {Array.isArray(invoiceData?.item) && invoiceData?.item.map((item, ix) => {

                return (
                    <Grid
                        key={ix.toString()}
                        container
                        spacing={2}
                        columns={12}
                        sx={{
                            mb: theme => theme.spacing(1),
                            alignItems: 'center', p: { xs: 1, sm: 0, lg: 0 },
                            borderWidth: { xs: 1, sm: 0, lg: 0 },
                            borderRadius: { xs: 1, sm: 0, lg: 0 }
                        }}
                    >
                        <Grid size={{ xs: 12, sm: 5, lg: 5 }}>
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    width: "100%",
                                    gap: 2
                                }}>
                                <FormControl>
                                    <TextField
                                        id="name"
                                        type="text"
                                        name="name"
                                        placeholder="ürün adı"
                                        title="ürün adı"
                                        autoComplete="name"
                                        autoFocus
                                        required
                                        fullWidth
                                        variant="outlined"
                                        color={"primary"}
                                        value={item.name || ""}
                                        // defaultValue={item.name || ""}
                                        onChange={(event) => props.handleLineItemChange(ix, event, "name")}
                                        sx={{ ariaLabel: "name", }}
                                    />
                                </FormControl>
                            </Box>
                        </Grid>
                        <Grid size={{ xs: 12, sm: 1, lg: 1 }} >
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    width: "100%",
                                    gap: 2
                                }}>
                                <FormControl>
                                    <TextField
                                        id="quantity"
                                        type="number"

                                        name="quantity"
                                        placeholder="ürün adedi"
                                        title="ürün adedi"
                                        autoComplete="quantity"
                                        autoFocus
                                        required
                                        fullWidth
                                        variant="outlined"
                                        color={"primary"}
                                        value={item.quantity || ""}
                                        onChange={(event) => props.handleLineItemChange(ix, event, "quantity")}
                                        // defaultValue={item.quantity}
                                        inputProps={{ min: 1, style: { textAlign: 'right' } }}
                                        sx={{ ariaLabel: "quantity" }}
                                    />
                                </FormControl>
                            </Box>


                        </Grid>
                        <Grid size={{ xs: 12, sm: 2, lg: 2 }} >
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "row",
                                    width: "100%",
                                    gap: 2,
                                    alignItems: 'center',
                                    justifyContent: 'flex-end'
                                }}>
                                <span sx={{ width: '70%', textAlign: 'right' }} title={'toplam kdv tutarı: ' + parseFloat(item.satirToplamKDVTutari).toFixed(2)}>
                                    {parseFloat(item.urunKDVTutari).toFixed(2)}
                                </span>

                                <FormControl>
                                    <TextField
                                        id="taxRate"
                                        type="number"
                                        name="taxRate"
                                        placeholder="kdv oranı"
                                        title={'toplam kdv tutarı: ' + parseFloat(item.satirToplamKDVTutari).toFixed(2)}
                                        // title="kdv oranı"
                                        autoComplete="taxRate"
                                        autoFocus
                                        required
                                        fullWidth
                                        variant="outlined"
                                        color={"primary"}
                                        value={item.taxRate * 100 || ""}
                                        onChange={(event) => props.handleLineItemChange(ix, event, "taxRate")}
                                        // defaultValue={item.taxRate * 100}
                                        inputProps={{ min: 0, style: { textAlign: 'right', minWidth: '40px', maxWidth: '60px' } }}
                                        sx={{ ariaLabel: "taxRate" }}
                                    />
                                </FormControl>
                            </Box>

                        </Grid>
                        <Grid size={{ xs: 12, sm: 1.5, lg: 1.5 }} >
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    width: "100%",
                                    gap: 2,
                                }}>
                                <FormControl>
                                    <TextField
                                        id="urunFiyati"
                                        name="urunFiyati"
                                        // disabled
                                        placeholder="KDV hariç ürün fiyatı"
                                        // title="KDV hariç ürün fiyatı"
                                        title={'KDVsiz: ' + parseFloat(item.urunFiyatiKDVsiz).toFixed(2).toString()}
                                        autoComplete="urunFiyati"
                                        autoFocus
                                        required
                                        fullWidth
                                        variant="outlined"
                                        color={"primary"}
                                        onChange={(event) => props.handleLineItemChange(ix, event, "urunFiyati")}
                                        value={(item.urunFiyati) || ""}
                                        // defaultValue={parseFloat(item.urunFiyati).toFixed(2)}
                                        inputProps={{ min: 0, style: { textAlign: 'right', minWidth: '60px', } }}
                                        sx={{ ariaLabel: "urunFiyati", }}
                                    />
                                </FormControl>
                            </Box>
                        </Grid>
                        <Grid size={{ xs: 12, sm: 1.5, lg: 1.5 }}>
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    width: "100%",
                                    gap: 2,
                                }}>
                                <FormControl>
                                    <TextField
                                        id="itemPrice"
                                        disabled
                                        type="number"
                                        name="itemPrice"
                                        placeholder="KDV dahil ürün fiyatı"
                                        // title="KDV dahil ürün fiyatı"
                                        title={'KDVsiz: ' + parseFloat(item.satirToplamKDVHaricUrunFiyati).toFixed(2).toString()}
                                        autoComplete="itemPrice"
                                        autoFocus
                                        required
                                        fullWidth
                                        variant="outlined"
                                        color={"primary"}
                                        value={parseFloat(item.satirToplamKDVDahilUrunFiyati).toFixed(2) || ""}
                                        // defaultValue={parseFloat(item.satirToplamKDVDahilUrunFiyati).toFixed(2)}
                                        inputProps={{ min: 0, style: { textAlign: 'right', minWidth: '70px' } }}
                                        sx={{ ariaLabel: "itemPrice" }}
                                    />
                                </FormControl>
                            </Box>
                        </Grid>

                        <Grid size={{ xs: 12, sm: 1, lg: 1 }}>
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    width: "100%",
                                    gap: 2,
                                }}>
                                <Button size="small"
                                    // onClick={() => itemsil(item.id)}
                                    onClick={() => props.handleRemoveLineItem(ix, item)}
                                    variant="outlined" startIcon={<DeleteForeverIcon />}>
                                    Sil
                                </Button>
                            </Box>
                        </Grid>
                    </Grid>
                )
            })}
            <Grid
                container
                spacing={2}
                columns={12}
                sx={{ mt: 3, pt: 2, borderTopWidth: 1, borderTopStyle: 'solid', borderTopColor: 'divider' }}
            >
                <Grid size={{ xs: 12, sm: 8, lg: 8 }} />
                <Grid size={{ xs: 12, sm: 4, lg: 4 }} textAlign={'right'}>
                    
                </Grid>
            </Grid>
        </>
    )
}

export default OrdersExcel;
