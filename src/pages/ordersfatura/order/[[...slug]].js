/* eslint-disable react/jsx-key */
/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

import React, { useEffect, useState, useContext } from 'react'
import Head from 'next/head'
import dynamic from 'next/dynamic'
import localforage from 'localforage';
import { useRouter } from 'next/router'
import Stack from "@mui/material/Stack"
import Layout from "@/lib/layouts/layout.user"
import { ColorModeContext } from "@/lib/context/Provider.themeDarkMode";
import { signIn, signOut, useSession } from 'next-auth/react'
import Box from "@mui/material/Box"
import Typography from "@mui/material/Typography"
import Button from '@mui/material/Button';
import LinearProgress from '@mui/material/LinearProgress';
import { titlecase, camalize } from '@/lib/fnx/fnx.cli'
import Grid2 from '@mui/material/Grid2'
import Card from '@mui/material/Card';
import MuiCard from "@mui/material/Card"
import CardContent from '@mui/material/CardContent';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import IconButton from '@mui/material/IconButton';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CreateInvoice from '@/components/pages/user.orders.createInvoiceModal.js';
import AddLocationAltIcon from '@mui/icons-material/AddLocationAlt';
import AddBoxIcon from '@mui/icons-material/AddBox';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
// import InvoiceViewer from '@/components/pages/user.invoice.displayModal'

// const InvoiceViewer = dynamic(() =>
//     import('@/components/pages/user.invoice.displayModal'), {
//         loading: () => <p>Loading...</p>
//     }
// )

const InvoiceViewer = dynamic(() => import('@/components/pages/user.invoice.displayModal'), {
    ssr: false,
})

import {
    Table,
    TableHead,
    TableBody,
    TableRow,
    TableCell,
    TableContainer,
    Paper,
    Chip,
    Tooltip
} from '@mui/material';


export default function Dashboard(props) {

    const { ...rest } = props;
    const router = useRouter();
    const { slug } = router.query
    // const [query, setOrderQuery] = useState(router.query)

    const { status, data: session } = useSession({
        required: true,
        onUnauthenticated() {
            signIn();
        },
    })

    const [mounted, setmounted] = React.useState(false);
    const { colorMode, drawerCollapsed } = useContext(ColorModeContext);
    useEffect(() => {
        setmounted(true)
        colorMode.setCurrPage('orders');
        colorMode.setCurrBread('AnaSayfa/Siparişler/Sipariş Detayları');
    }, []);

    if (status === "loading") {
        return "Loading, please wait..."
    }

    return (
        <>
            <Head>
                <title>Shopivo Sipariş Detayları</title>
            </Head>
            <Layout session={session} {...props}
                pgTitle={'Siparişler'}
                headerPosition='fixed'
            // headerBgColor={'lightblue'}
            // headerItems={<Typography>HelloTitle</Typography>}
            >
                <Box sx={{ width: "100%", maxWidth: { sm: "100%", md: "1700px" }, px: 3 }}>

                    <Stack sx={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>

                        <Stack sx={{ flexDirection: 'row', justifyItems: 'center', alignItems: 'center', mb: 2 }}>
                            <IconButton title='x' onClick={() => router.back()}><ChevronLeftIcon /> </IconButton>
                            <Typography component="h2" variant="h6" sx={{ mb: 0, ml: 1 }}>
                                Sipariş Detayları {router.query && router.query.name ? ' / ' + router.query?.name + ' / ' + titlecase(router.query?.cFullName) : ''}
                            </Typography>
                        </Stack>
                        <CardPreNextOrder {...props} slug={slug} query={router.query} />

                    </Stack>
                    <Order {...props} session={session} slug={slug} query={router.query} />
                </Box>
            </Layout>
        </>
    )
}


const Order = props => {
    const router = useRouter();
    const { session, slug = [] } = props;
    const { user } = session ? session : {};
    const { token, refreshToken } = user ? user : {};

    // const { toggleColorMode: mode } = useColorMode();
    const [data, setData] = useState(false)
    const [loading, setloading] = useState(false)
    // console.log('porpz', props);
    const fetchOrder = async ({
        orderID,
    }) => {
        return new Promise(async function (resolve, reject) {
            try {
                var uri = "/api/db/getorder";
                uri += '/' + orderID;
                // console.info('url', uri);

                const res = await fetch(uri, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'X-Host': 'Subanet.com',
                    },
                })
                if (res) {
                    // console.log('res', res)
                    if (res.status == 200) {
                        const datax = await res.json()
                        resolve(datax.data)
                    } else {
                        console.log('error', res.error?.message)
                        reject(res.error?.message)
                    }

                } else {
                    reject(false)
                }
            }
            catch (e) {
                reject(e)
            }
        });
    }
    // const fnFetchOrder = async ({ page }) => {
    //     setloading(true);
    //     let dataStg = await fetchOrder({
    //         orderID: props?.slug && props?.slug[0],
    //     })
    //     setData(dataStg)
    //     setloading(false)
    // }

    useEffect(() => {
        const fetchOrdersInit = async () => {
            setloading(true);
            let ordersData = await fetchOrder({
                orderID: props?.slug && props?.slug[0]
            })
            setData(ordersData)
            setloading(false)
        };
        fetchOrdersInit();
    }, [props?.slug])
    return (
        <>
            {loading && <Box sx={{ position: 'absolute', top: 0, left: 0, right: 0, height: 2 }}><LinearProgress /></Box>}
                
            <Grid2
                container
                spacing={2}
                columns={12}
                sx={{ mb: theme => theme.spacing(2), minHeight: '600px' }}
            >
                <Grid2 size={{ xs: 12, sm: 8, lg: 9 }} sx={{
                    borderWidth: 0,
                }}>
                    {!loading && (
                        <Card variant="outlined" sx={{ m: 0 }}>
                            <OOrderHeader data={data} token={token} />
                            <OOrderItems data={data} token={token} />
                        </Card>
                    )}
                    {!loading && <OrderInvoices data={data} token={token} session={session} />}
                </Grid2>

                <Grid2 size={{ xs: 12, sm: 4, lg: 3 }} sx={{ borderWidth: 0, }}>
                    {!loading && (
                        <Card variant="outlined" sx={{ m: 0 }}>
                            <OCustomerInfo data={data} />
                        </Card>
                    )}
                </Grid2>
            </Grid2>
        </>
    )
}
const OOrderHeader = props => {
    const [data, setData] = useState(props.data ? props.data : false);
    useEffect(() => {
        setData(props.data)
    }, [props.data])

    let tDisc = props.data?.current_total_discounts && parseFloat(props.data?.current_total_discounts);
    let tDicsVar = tDisc ? true : false;
    let tPrice = parseFloat(props.data?.current_total_price) + (tDisc ? tDisc : 0);
    let tPriceNet = parseFloat(props.data?.total_price) - parseFloat(props.data?.total_tax)

    return (<>
        <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography component="h2" variant="h6" sx={{ mb: 2, p: 1 }} onClick={() => console.log(data)}>
                Özet
            </Typography>
            <Box sx={{ flexDirection: 'row' }}>
                <Button sx={{ backgroundColor: '#cecece', mx: 2 }}
                    component="label"
                    role={undefined}
                    // variant="contained"
                    tabIndex={-1}
                    onClick={() => window.open(props.data?.order_status_url, '_blank')}
                    startIcon={<OpenInNewIcon />}
                >Shopify</Button>

                <CreateInvoice data={data} token={props.token} />
            </Box>
        </Stack>
        {data?.note && (
            <Card variant="outlined" sx={{ backgroundColor: '#ffcc00', m: 2 }}>
                <CardContent>
                    <Typography gutterBottom sx={{ color: 'text.secondary', fontSize: 12, fontStyle: 'italic' }}>
                        Customer Note
                    </Typography>
                    <Typography sx={{ color: 'text.primary', mb: 1.5 }}>
                        {data.note}
                    </Typography>
                </CardContent>
            </Card>
        )}

        <Stack sx={{ flexDirection: 'row', justifyContent: 'flex-start', borderBottomWidth: 0.5 }}>
            <Box sx={{ mx: 1 }}>
                <span align={'right'} style={{ color: '#666', fontStyle: 'italic' }}>genel toplam</span><br />
                {props.data?.total_line_items_price} {props.data?.currency}
            </Box>
            {tDicsVar && (
                <>
                    <Box sx={{ mx: 2 }}>
                        <span align={'right'} style={{ color: '#666', fontStyle: 'italic' }}>indirim toplam</span><br />
                        {props.data?.total_discounts}
                    </Box>

                    <Box sx={{ mx: 2 }}>
                        <span align={'right'} style={{ color: '#666', fontStyle: 'italic' }}>fatura toplam</span><br />
                        {props.data?.total_price}
                    </Box>
                </>
            )}
            <Box sx={{ mx: 2 }}>
                <span align={'right'} style={{ color: '#666', fontStyle: 'italic' }}>kdv. total</span><br />
                {props.data?.total_tax}
            </Box>
            <Box sx={{ mx: 2 }}>
                <span align={'right'} style={{ color: '#666', fontStyle: 'italic' }}>kdv. hariç</span><br />
                <span align={'right'}>{!isNaN(tPriceNet) && parseFloat(tPriceNet).toFixed(2)}</span>
            </Box>
        </Stack>
    </>)
}
const OOrderItems = props => {
    const router = useRouter();
    const [data, setData] = useState(props.data ? props.data : false);
    useEffect(() => {
        setData(props.data)
    }, [props.data])

    return (<>
        {/* <Typography component="h2" variant="h6" sx={{ mb: 2, p: 1 }}>
            Order Items
        </Typography> */}

        <Box mt={2} my={1}>
            <TableContainer component={Paper}>
                <Table size="small" aria-label="a dense table">
                    <TableHead sx={{ borderBottomWidth: 2, borderBottomColor: '#999' }}>
                        <TableRow>
                            <TableCell sx={{ textAlign: 'left', borderRightWidth: 1 }}>ürün</TableCell>
                            <TableCell sx={{ textAlign: 'left', borderRightWidth: 1 }}>adet</TableCell>
                            <TableCell sx={{ textAlign: 'left', borderRightWidth: 1 }}>ürün satış fiyatı</TableCell>
                            <TableCell sx={{ textAlign: 'left', borderRightWidth: 1 }}>indirim</TableCell>
                            <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }}>kdv dahil tutar</TableCell>
                            {/* <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }}>kdv hatic tutar</TableCell> */}
                            <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }}>kdv toplamı</TableCell>
                            <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }}>Satır toplam</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>

                        {Array.isArray(data?.line_items) && data?.line_items.map((item, ix) => {
                            let urunSatisFiyati = parseFloat(item.price)
                            let discAmount = item.discount_allocations && Array.isArray(item.discount_allocations) && item.discount_allocations[0]?.amount;
                            let urunFiyati = item.price && parseFloat(urunSatisFiyati - (discAmount || 0))
                            let kdvOrani = item.tax_lines && Array.isArray(item.tax_lines) && item.tax_lines[0]?.rate && parseFloat(item.tax_lines[0]?.rate);
                            let urunFiyatiKDVsiz = urunFiyati / (1 + kdvOrani);
                            let urunKDVTutari = urunFiyatiKDVsiz * kdvOrani
                            let urunAdedi = item.quantity;
                            let satirToplamKDVDahilUrunFiyati = urunFiyati * urunAdedi;
                            let satirToplamKDVHaricUrunFiyati = urunFiyatiKDVsiz * urunAdedi;
                            let satirToplamKDVTutari = urunKDVTutari * urunAdedi
                            return (

                                <TableRow key={(item.id)} sx={{ borderBottomColor: ix % 2 == 0 ? 'blue' : 'red', borderBottomWidth: 1, my: 1 }}>
                                    <TableCell sx={{ borderRightWidth: 1 }}>{titlecase(item.name)}</TableCell>
                                    <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }}>{urunAdedi}</TableCell>
                                    <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }}>{urunSatisFiyati.toFixed(2)}</TableCell>
                                    <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }} title={JSON.stringify(data?.discount_applications)}>{discAmount}</TableCell>
                                    <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }} title={urunFiyatiKDVsiz}>{urunFiyati.toFixed(2)}</TableCell>
                                    <TableCell sx={{ textAlign: 'right', mr: 4 }} title={(urunKDVTutari).toFixed(2)}>
                                        <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>
                                            <Chip sx={{ mx: 1 }} label={kdvOrani * 100 + '%'} />
                                            {(satirToplamKDVTutari).toFixed(2)} {item.currency}
                                        </Stack>
                                    </TableCell>
                                    <TableCell sx={{ textAlign: 'right', borderRightWidth: 1 }} title={satirToplamKDVHaricUrunFiyati}>{satirToplamKDVDahilUrunFiyati.toFixed(2)}</TableCell>
                                    {/* <TableCell>
                                        { parseFloat(item.price - (discAmount || 0)) / (parseFloat(item.tax_lines[0]?.rate) + 1) * parseFloat(item.tax_lines[0]?.rate)}
                                    </TableCell> */}

                                </TableRow>

                            )
                        })}
                    </TableBody>
                </Table>
            </TableContainer>
        </Box>


    </>)
}
const OCustomerInfo = props => {
    const [data, setData] = useState(props.data ? props.data : false);
    useEffect(() => {
        setData(props.data)
    }, [props.data])
    return (<>

        <Tooltip title={'custID: ' + (props.data && data.customer?.id) + '\n' +
            'created_at: ' + (props.data && data.customer?.created_at) + '\n' +
            'updated_at: ' + (props.data && data.customer?.updated_at) + '\n'
        }>
            <Typography component="h2" variant="h6" sx={{ mb: 1, p: 1 }}>
                Müşteri
            </Typography>
        </Tooltip>
        <Card variant="outlined">
            <CardContent>
                <Typography variant="subtitle1" sx={{ p: 0 }}>
                    {props.data && titlecase(data.customer?.first_name)} {props.data && titlecase(data.customer?.last_name)}
                </Typography>
                <Typography variant="body2" sx={{ p: 0 }}>
                    {(props.data && data.customer?.email) || (props.data && data?.email) || (props.data && data?.contact_email)}
                    {props.data && data.customer?.verified_email && <span style={{ fontSize: '8px' }}>&nbsp;<CheckBoxIcon sx={{ color: 'blue', fontSize: 18 }} /></span>}
                </Typography>
                <Typography variant="body2" sx={{ pb: 2 }}>
                    {props.data && data.customer?.phone || props.data && data.customer?.default_address?.phone}
                </Typography>
                <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Box sx={{ pr: 1 }}>
                        <AddLocationAltIcon sx={{ fontSize: 24, color: 'red', cursor: 'pointer' }}
                            onClick={() => window.open('https://maps.google.com/?q=' + data.shipping_address?.address1 + ' ' + data.shipping_address?.address2 + ' ' + data.shipping_address?.city, '_blank')}
                        />
                    </Box>
                    <Typography variant="body2" sx={{ py: 1 }}>
                        {props.data && data.shipping_address?.city} {props.data && data.shipping_address && data.shipping_address?.country ? ' / ' + data.shipping_address?.country : ''}
                    </Typography>
                </Stack>
                <Stack sx={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                    <Box>
                        <Typography variant="caption" sx={{ py: 1, color: '#333', fontStyle: 'italic' }}>
                            {props.data && data.shipping_address?.address1}
                        </Typography><br />
                        <Typography variant="caption" sx={{ color: '#333', fontStyle: 'italic' }}>
                            {props.data && data.shipping_address?.address2} </Typography>
                        {props.data && data.shipping_address?.latitude && (
                            <Typography variant="caption"
                                onClick={() => window.open('https://maps.google.com/?q=' + (data.shipping_address?.latitude + ', ' + data.shipping_address?.longitude), '_blank')}
                                sx={{ color: '#333', fontStyle: 'italic', cursor: 'pointer', color: 'blue' }}>
                                {(data.shipping_address?.latitude + ', ' + data.shipping_address?.longitude)}
                            </Typography>)}
                    </Box>
                </Stack>
            </CardContent>
        </Card>
        <Typography component="h2" variant="h6" sx={{ mt: 2, p: 1 }}>
            Gönderim ve Ödeme
        </Typography>

        <Card variant="outlined">
            <CardContent>
                <Stack sx={{ flexDirection: 'row', justifyContent: 'flex-start', borderBottomWidth: 0.5, mb: 2 }}>
                    <Box sx={{ mx: 0 }}>
                        <span align={'right'} style={{ color: '#666', fontStyle: 'italic' }}>ödeme d.</span><br />
                        {data?.financial_status}
                    </Box>
                    <Box sx={{ mx: 1 }}>
                        <span align={'right'} style={{ color: '#666', fontStyle: 'italic' }}>sevk d.</span><br />
                        {data?.fulfillment_status}
                    </Box>
                </Stack>
                <Stack sx={{ flexDirection: 'column', justifyContent: 'flex-start', borderBottomWidth: 0.5 }}>
                    <Box sx={{ mx: 0, mb: 2 }}>
                        <span align={'right'} style={{ color: '#666', fontStyle: 'italic' }}>siparis no</span><br />
                        {data?.order_number} - {data?.number}
                    </Box>
                    <Box sx={{ mx: 0, mb: 2 }}>
                        <span align={'right'} style={{ color: '#666', fontStyle: 'italic' }}>sipariş yaratma tarihi</span><br />
                        <span align={'right'}>{data?.created_at}</span>
                    </Box>
                    <Box sx={{ mx: 0, mb: 2 }}>
                        <span align={'right'} style={{ color: '#666', fontStyle: 'italic' }}>tamamlanma tarihi</span><br />
                        <span align={'right'}>{data?.closed_at}</span>
                    </Box>
                    <KargoEtiketi {...props} />
                </Stack>
            </CardContent>
        </Card>
    </>)
}
const Order_Transactions = props => {
    return (
        <>
            <Flex
                // bg="#edf3f8"
                // _dark={{
                //     bg: "#3e3e3e",
                // }}
                p={2}
                w="full"
                alignItems="center"
                justifyContent="center"
            >
                <Box
                    mx="auto"
                    px={4}
                    py={2}
                    w="full"
                    rounded="lg"
                    shadow="lg"
                    bg="white"
                    _dark={{
                        bg: "gray.800",
                    }}
                // maxW="2xl"
                >
                    <Flex justifyContent="space-between" alignItems="center">
                        <chakra.span
                            fontSize="sm"
                            color="gray.600"
                            _dark={{
                                color: "gray.400",
                            }}
                        >
                            Ödeme Hareketleri
                        </chakra.span>
                    </Flex>

                    <Box mt={2}>
                        <Stack spacing="6">
                            {Array.isArray(props.data?.transactions) && props.data?.transactions.map((item) => (
                                <Box
                                    key={item.id}
                                    mt={2}
                                    color="gray.600"
                                    _dark={{
                                        color: "gray.300",
                                    }}
                                >
                                    <Text> {item.id} </Text>
                                    <Text> {item.gateway}</Text>
                                    <Text> {item.payment_id}</Text>
                                    <Text> {item.kind}</Text>
                                    <Text> {item.amount} {item.currency}</Text>
                                    <Text> {item.processed_at}</Text>
                                    <Text> {item.status}</Text>
                                </Box>
                            ))}
                        </Stack>
                    </Box>

                </Box>
            </Flex>
            {/* <Text>{JSON.stringify(props.data?.transactions)}</Text> */}
        </>
    )
}
const OrderInvoices = props => {
    const { data: orderData } = props;

    // 
    const [data, setData] = useState(props.data ? props.data.invoices : false);
    useEffect(() => {
        // console.log('props.data?.invoices', props.data?.invoices)
        setData(props.data?.invoices);
    }, [props.data])


    return (
        <>
            <Stack sx={{ my: 2 }}>
                <Card variant="outlined">
                    <Typography variant='h6'>İlgili Faturalar</Typography>

                    {Array.isArray(data) && data.length !== 0 && <Grid2
                        container
                        spacing={2}
                        columns={12}
                        sx={{ mb: theme => theme.spacing(0), borderTopWidth: 0.5, py: 2 }}
                    >
                        <Grid2 size={{ xs: 12, sm: 2, lg: 2 }} sx={{
                            borderWidth: 0,
                        }}>
                            <Typography>
                                {'belgeNumarasi'}
                            </Typography>
                        </Grid2>

                        <Grid2 size={{ xs: 12, sm: 2, lg: 2 }} >
                            <Typography>
                                {'belgeTarihi'}
                            </Typography>
                        </Grid2>

                        <Grid2 size={{ xs: 12, sm: 1, lg: 1 }}>
                            <Typography>
                                {'totalVAT'}
                            </Typography>
                        </Grid2>
                        <Grid2 size={{ xs: 12, sm: 2, lg: 2 }}>
                            <Typography>
                                {'gTotal'}
                            </Typography>
                        </Grid2>
                        <Grid2 size={{ xs: 12, sm: 2, lg: 2 }}>
                            <Typography>
                                {'gTotalInclVAT'}
                            </Typography>
                        </Grid2>
                        <Grid2 size={{ xs: 12, sm: 2, lg: 2 }}>
                            <Typography>
                                {'onayDurumu'}
                            </Typography>
                        </Grid2>
                        <Grid2 size={{ xs: 12, sm: 0.5, lg: 0.5 }}>
                            <Typography>
                                {' '}
                            </Typography>
                        </Grid2>
                    </Grid2>
                    }

                    {Array.isArray(data) && data.map((i, ix) => {
                        return (
                            <Grid2
                                key={ix.toString()}
                                container
                                spacing={2}
                                columns={12}
                                sx={{ mb: theme => theme.spacing(0), borderTopWidth: 0.5, py: 0.5 }}
                            >
                                <Grid2 size={{ xs: 12, sm: 2, lg: 2 }} sx={{
                                    borderWidth: 0, pt: 1
                                }}>
                                    <Typography>
                                        <span title={'ettn: ' + i?.draftInvoiceDetails?.ettn}>{i?.draftInvoiceDetails ? i.draftInvoiceDetails?.belgeNumarasi : i.draftFirstInvoiceDetails?.belgeNumarasi}</span>
                                    </Typography>
                                </Grid2>

                                <Grid2 size={{ xs: 12, sm: 2, lg: 2 }} sx={{ pt: 1 }} >
                                    <Typography>
                                        {i.draftInvoiceDetails?.belgeTarihi || i.draftFirstInvoiceDetails?.belgeTarihi } {i.time}
                                    </Typography>
                                </Grid2>

                                <Grid2 size={{ xs: 12, sm: 1, lg: 1 }} sx={{ pt: 1 }}>
                                    <Typography>
                                        {i.invData?.totalVAT}
                                    </Typography>
                                </Grid2>
                                <Grid2 size={{ xs: 12, sm: 2, lg: 2 }} sx={{ pt: 1 }}>
                                    <Typography>
                                        {i.invData?.grandTotal}
                                    </Typography>
                                </Grid2>
                                <Grid2 size={{ xs: 12, sm: 2, lg: 2 }} sx={{ pt: 1 }}>
                                    <Typography>
                                        {i.invData?.grandTotalInclVAT}
                                    </Typography>
                                </Grid2>
                                <Grid2 size={{ xs: 12, sm: 1, lg: 1 }} sx={{ pt: 1 }}>
                                    <Typography>
                                        {i.draftInvoiceDetails ? i.draftInvoiceDetails?.onayDurumu : 'Onaylanmadı'}
                                    </Typography>
                                </Grid2>

                                <Grid2 size={{ xs: 12, sm: 1, lg: 1 }} sx={{ mr: 0.5 }}>
                                    {/* <Typography>
                                        {'Göster'}
                                    </Typography> */}
                                    {i?.invData && (
                                        <InvoiceViewer data={i?.invData} session={props.session} />
                                    )}
                                </Grid2>

                            </Grid2>
                        )
                    })}

                    {Array.isArray(data) && data.length == 0 && (
                        <Box
                            sx={{
                                display: "flex",
                                flexDirection: "column",
                                width: "100%",
                                gap: 2,
                                borderTopWidth: 1,
                                pt: 1,
                            }}>
                            <Box sx={{ alignSelf: 'flex-end' }}>
                                <CreateInvoice data={props.data} token={props.token} />
                            </Box>
                        </Box>
                    )}
                </Card>
            </Stack>
        </>
    )
}

const CardPreNextOrder = props => {
    const router = useRouter();
    const [modalData, setmodalData] = React.useState(false);
    const [aID, setaID] = React.useState({
        once: 0,
        sonra: 0,
    });
    const goto = (orderID, refData) => {
        // console.log('orderID', orderID)
        router.push({
            pathname: '/orders/order/' + orderID,
            query: { ...refData, refreshMe: 1 },
        });
    }
    useEffect(() => {
        const getFromDB = async (v) => {
            var orderlist = await localforage.getItem('orderlist');
            if (orderlist && Array.isArray(orderlist.data)) {
                let orderListData = orderlist.data;
                let Idx = orderListData.findIndex(i => i.orderID == v);
                // console.log('ıdx', Idx);
                if (Idx > -1) {
                    let aIDStg = {
                        once: Idx > 0 ? orderListData[Idx - 1].orderID : 0,
                        onceData: Idx > 0 ? orderListData[Idx - 1] : {},
                        sonra: orderListData[Idx + 1].orderID,
                        sonraData: orderListData[Idx + 1]
                    };
                    // console.log('setaID', aIDStg);
                    setaID(aIDStg)
                }
            }
        }
        if (props.slug) {
            getFromDB(props.slug[0]);
        } else {
        }
    }, [props.slug])

    return (
        <>
            <Stack sx={{ flexDirection: 'row' }}>
                <Box sx={{ mr: 1, width: '150px', }}>
                    {aID?.once !== 0 && (
                        <Typography sx={{
                            py: 0.5, borderWidth: 1, borderColor: '#cecece', borderRadius: 2,
                            cursor: 'pointer', textAlign: 'center', px: 1,
                        }} onClick={() => goto(aID.once, aID.onceData)}>Önceki Sipariş</Typography>
                    )}
                </Box>
                <Box sx={{ width: '150px', borderWidth: 0, }}>
                    {aID?.sonra !== 0 && (
                        <Typography sx={{
                            py: 0.5, borderWidth: 1, borderColor: '#cecece', borderRadius: 2,
                            cursor: 'pointer', textAlign: 'center', px: 1,
                        }} onClick={() => goto(aID.sonra, aID.sonraData)}>Sonraki Sipariş</Typography>
                    )}
                </Box>
            </Stack>
        </>
    )
}

import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import useMediaQuery from '@mui/material/useMediaQuery';
import { styled, useTheme } from '@mui/material/styles';

import CancelIcon from '@mui/icons-material/Cancel';

const Card2 = styled(MuiCard)(({ theme }) => ({
    display: "flex",
    flexDirection: "column",
    alignSelf: "center",
    width: "100%",
    padding: theme.spacing(4),
    gap: theme.spacing(2),
    margin: "auto",
    [theme.breakpoints.up("sm")]: {
        maxWidth: "450px"
    },
    boxShadow:
        "hsla(220, 30%, 5%, 0.05) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.05) 0px 15px 35px -5px",
    ...theme.applyStyles("dark", {
        boxShadow:
            "hsla(220, 30%, 5%, 0.5) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.08) 0px 15px 35px -5px"
    })
}))

const KargoEtiketi = props => {

    const [open, setOpen] = React.useState(false);
    const [loading, setloading] = React.useState(false);
    const [loadingHTML, setloadingHTML] = React.useState(false);
    const [maxWidth, setMaxWidth] = React.useState('lg');
    const [scroll, setScroll] = React.useState('paper');
    const contentRef = React.useRef(null);

    const theme = useTheme();
    const fullScreen = useMediaQuery(theme.breakpoints.down('md'));

    const handleClickOpen = (scrollType) => () => {
        setOpen(true);
        // props.data && setmodalData(props.data)
        // setmailAddress(props.data?.email)
        // setmailName(props.data?.aliciUnvanAdSoyad || props.data?.name)
        setScroll(scrollType);
    };

    const handleClose = (event, reason) => {
        if (reason && reason === "backdropClick") {
            return;
        } else {
            // setmodalData(false)
            // setmailAddress(false)
            // console.log('close modal')
            setOpen(false);
        }
    };

    return (
        <>

            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    width: "100%",
                    gap: 2,
                    borderTopWidth: 1,
                    pt: 1,
                }}>
                <Button sx={{ backgroundColor: '#cecece', mb: 2 }}
                    component="label"
                    role={undefined}
                    // variant="contained" 
                    onClick={handleClickOpen('paper')}
                    startIcon={<OpenInNewIcon />}
                >Kargo Etiketi Yazdır</Button>

            </Box>
            <Dialog
                open={open}
                onClose={handleClose}
                scroll={'paper'}
                aria-labelledby="scroll-dialog-title"
                aria-describedby="scroll-dialog-description"
                fullWidth={true}
                maxWidth={fullScreen ? 'xl' : maxWidth}
            >
                <DialogTitle id="scroll-dialog-title">
                    <Stack sx={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="h6">
                            Kargo Etiketi Yazdır
                        </Typography>
                        <CancelIcon sx={{ fontSize: 30 }} onClick={() => handleClose()}></CancelIcon>

                    </Stack>
                </DialogTitle>
                {loadingHTML && <Box sx={{ position: 'absolute', top: 0, left: 0, right: 0, height: 2 }}><LinearProgress /></Box>}
                <>
                    <DialogContent dividers={scroll === 'paper'}>
                        <Grid2
                            container
                            spacing={2}
                            columns={12}
                            sx={{ mb: theme => theme.spacing(2), minHeight: '600px' }}
                        >
                            <Grid2 size={{ xs: 12, sm: 9, lg: 9 }} sx={{
                                // overflow: 'auto',
                            }}>
                                <Typography>data</Typography>
                            </Grid2>

                            <Grid2 size={{ xs: 12, sm: 3, lg: 3 }} sx={{
                                backgroundColor: '#efefef', p: 1
                            }}>
                                <MuiCard sx={{ mb: 2 }}>
                                    <Box>
                                        x
                                    </Box>
                                </MuiCard>
                            </Grid2>
                        </Grid2>
                    </DialogContent>
                    <DialogActions>
                        <Button onClick={() => handleClose()}>Kapat</Button>
                        {/* <LoadingButton loading={loading} disabled={loading} onClick={console.log}>GIB e gönder</LoadingButton> */}
                    </DialogActions>
                </>
            </Dialog>

        </>
    )
}

const KargoEtiketiForm = props => {
    return (
        <>
        form burada...
        </>
    )
}