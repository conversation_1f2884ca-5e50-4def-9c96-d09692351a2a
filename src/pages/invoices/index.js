import React, { useEffect, useState, useContext } from 'react'
import Head from 'next/head'
import { useRouter } from 'next/router'
import localforage from 'localforage';
import moment from 'moment';
import Stack from "@mui/material/Stack"
import Layout from "@/lib/layouts/layout.user"
import { ColorModeContext } from "@/lib/context/Provider.themeDarkMode";
import { signIn, signOut, useSession } from 'next-auth/react'
import Box from "@mui/material/Box"
import Typography from "@mui/material/Typography"
import Button from '@mui/material/Button';
import LoadingButton from "@mui/lab/LoadingButton"
import LinearProgress from '@mui/material/LinearProgress';
import { titlecase } from '@/lib/fnx/fnx.cli'
import CloudSyncIcon from '@mui/icons-material/CloudSync';
import dynamic from 'next/dynamic'

import NoneOrder from '@/components/pages/user.orders.none'
import ButtonGroup from '@mui/material/ButtonGroup';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Grow from '@mui/material/Grow';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';
import MenuItem from '@mui/material/MenuItem';
import MenuList from '@mui/material/MenuList';
import SyncAltIcon from '@mui/icons-material/SyncAlt';

import Menu from '@mui/material/Menu';
import Divider from '@mui/material/Divider';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';

import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import StorageIcon from '@mui/icons-material/Storage';
import CircularProgress from '@mui/material/CircularProgress';

import { alpha, styled } from '@mui/material/styles';
import { DataGrid, GridRowsProp, GridColDef, gridClasses, GridToolbar } from '@mui/x-data-grid';

const InvoiceViewer = dynamic(() =>
    import('@/components/pages/user.invoice.displayModal')
)

// import InvoiceViewer from '@/components/pages/user.invoice.displayModal'
const appSettingKey = "appSetting";

export default function Dashboard(props) {
    const { status, data: session } = useSession({
        required: true,
        onUnauthenticated() {
            signIn();
        },
    });

    const { user } = session ? session : {};
    const { token, refreshToken } = user ? user : {};

    const { colorMode, drawerCollapsed } = useContext(ColorModeContext);
    useEffect(() => {
        colorMode.setCurrPage('invoices');
        colorMode.setCurrBread('AnaSayfa/Faturalar');
    }, []);

    if (status === "loading") {
        return "Loading, please wait..."
    }
    return (
        <>
            <Head>
                <title>Shopivo Invoices</title>
            </Head>
            <Layout session={session} {...props}
                pgTitle={'GIB Faturalar'}
                headerPosition='fixedt'
            // headerBgColor={'lightblue'}
            // headerItems={<Typography>HelloTitle</Typography>}
            >
                <Box sx={{ width: "100%", maxWidth: { sm: "100%", md: "1700px" }, px: 3 }}>
                    <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', mb: 4, pt: { xs: 8, sm: 2, lg: 2 }  }}>
                        <Typography component="h2" variant="h6" sx={{ mb: 2 }}>
                            GIB Faturalar
                        </Typography>
                        <Box sx={{ flexDirection: 'row' }}>
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    width: "100%",
                                    gap: 2,
                                    borderTopWidth: 1,
                                    pt: 1,
                                }}>
                                <CustomizedMenus {...props} session={session} token={token} />
                            </Box>

                        </Box>
                    </Stack>
                    {/* <InvoicesTable {...props} /> */}
                    <InvoicesGrid {...props} session={session} token={token} />
                    
                </Box>
            </Layout>
        </>
    )
}
 
const ODD_OPACITY = 0.2;
const StripedDataGrid = styled(DataGrid)(({ theme }) => ({
    [`& .${gridClasses.row}.even`]: {
        backgroundColor: theme.palette.grey[300],
        '&:hover, &.Mui-hovered': {
            backgroundColor: alpha(theme.palette.primary.main, ODD_OPACITY),
            '@media (hover: none)': {
                backgroundColor: 'transparent',
            },
        },
        '&.Mui-selected': {
            backgroundColor: alpha(
                theme.palette.primary.main,
                ODD_OPACITY + theme.palette.action.selectedOpacity,
            ),
            '&:hover, &.Mui-hovered': {
                backgroundColor: alpha(
                    theme.palette.primary.main,
                    ODD_OPACITY +
                    theme.palette.action.selectedOpacity +
                    theme.palette.action.hoverOpacity,
                ),
                // Reset on touch devices, it doesn't add specificity
                '@media (hover: none)': {
                    backgroundColor: alpha(
                        theme.palette.primary.main,
                        ODD_OPACITY + theme.palette.action.selectedOpacity,
                    ),
                },
            },
        },
    },
}));

const InvoicesGrid = props => {
    const router = useRouter();
    const [data, setData] = useState(false);
    const [loading, setloading] = useState(true)
    const [syncingLocal, setsyncingLocal] = React.useState(false);

    const fnSyncLocalDB = async () => {
        return new Promise(async (resolve, reject) => {
            const url = `/api/db/listinvoicescloud/5000/0`;
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + props.token,
                        'X-Host': 'Subanet.com',
                    },
                });

                if (!response.ok) {
                    reject(false)
                }
                const data = await response.json();
                Array.isArray(data?.data) && data.data.map((d, ix) => {
                    d.id = ix + 1;
                });
                localforage.setItem("invoiceslist", data);
                console.log('Bilgisayarınızdaki fatura verileri güncellendi.');
                resolve(true)

                // if (response.statusText === 'OK') {
                // }
                // else {
                // }
            } catch (error) {
                console.log(error);
                reject(error)
            }
        });
    }
    const syncLocalDB = async () => {
        try {
            setsyncingLocal(true)
            await fnSyncLocalDB();
            setsyncingLocal(false)
            alert('Sunucu veritabanı fatura bilgileri Yerel bilgisayar verileri ile eşlendi!');
            setTimeout(function () {
                window.location.reload(1);
            }, 300);

        } catch (error) {
            setsyncingLocal(false)
            console.log(error);
        }
    }
    const fetchInvoicesData = async () => {
        try {
            setloading(true)
            const value = await localforage.getItem('invoiceslist');
            setloading(false)
            setData(value?.data)
            return value;
        } catch (err) {
            console.log(err);
            setloading(false)
            return false;
        }
    }

    const refreshData = async () => {
        try {
            await fetchInvoicesData(true);
        } catch (e) { }

    }

    useEffect(() => {
        fetchInvoicesData();
    }, []);

    const handleRowDoubleClick = row => {
        // console.log(row)
        router.push({
            pathname: 'invoices/invoice/' + row.belgeNumarasi,
            query: row,
        });
    }

    const columns = [
        { field: 'belgeNumarasi', headerName: 'Belge No', width: 160 , },
        { field: 'ettn', headerName: 'Ettn', width: 160,  },
        { field: 'aliciUnvanAdSoyad', headerName: 'Müşteri', flex: 2 },
        { field: 'belgeTarihi', headerName: 'Tarih', flex: 1 , },
        { field: 'onayDurumu', headerName: 'Durumu',  width: 90 },
        { field: 'htmlDocs', headerName: 'PDFDoc',  width: 90 },
        { field: 'emails', headerName: 'Emails',  width: 90 },
        { field: 'act', headerName: 'İşlem', flex: 1 , width: 180,
            renderCell: (params) => {
                return (
                    <>
                        <Stack sx={{ flexDirection: 'row', overflowX: 'auto' }}>
                            <InvoiceViewer data={params.row} 
                                session={props.session}
                                refreshParentTable={refreshData} />
                        </Stack>
                    </>
                );
            },
        }

    ];
    return (
        <>
            <Box
                sx={{
                    // height: 300,
                    width: '100%',

                    '& .super-app-theme--cell': {
                        backgroundColor: 'rgba(224, 183, 60, 0.55)',
                        color: '#1a3e72',
                        fontWeight: '600',
                    },
                    '& .super-app.negative': {
                        backgroundColor: 'rgba(157, 255, 118, 0.49)',
                        color: '#1a3e72',
                        fontWeight: '600',
                    },
                    '& .super-app.positive': {
                        backgroundColor: '#d47483',
                        color: '#1a3e72',
                        fontWeight: '600',
                    },
                }}
            >

                {data && <StripedDataGrid
                    rows={data}
                    columns={columns}
                    loading={loading}
                    rowHeight={35}
                    initialState={{
                      pagination: {
                        paginationModel: { pageSize: 15, page: 0 },
                      },
                    }}
                    pageSizeOptions={[5, 15, 25, 100]}
                    disableColumnMenu={true}

                    onRowDoubleClick={(row, event) => {
                        handleRowDoubleClick(row.row);
                    }}

                    slots={{ toolbar: GridToolbar }}
                    slotProps={{
                        loadingOverlay: {
                            variant: 'linear-progress',
                            noRowsVariant: 'skeleton',
                        },
                        toolbar: {
                            showQuickFilter: true,
                            printOptions: { disableToolbarButton: true },
                            csvOptions: {
                                fileName: 'futuresDaily',
                                delimiter: ';',
                                utf8WithBom: true,
                            }
                        }
                    }}
                />
                }


                {!data && !loading && (
                    <>
                        {syncingLocal && <Box sx={{ position: 'absolute', top: 0, left: 0, right: 0, height: 2 }}><LinearProgress /></Box>}
                        <NoneOrder />
                        <Box
                            sx={{
                                display: "flex",
                                flexDirection: "column",
                                width: "100%",
                                gap: 2,
                                borderTopWidth: 1,
                                pt: 1,
                            }}>
                            <LoadingButton sx={{ color: '#000' }}
                                component="label"
                                // variant="contained"
                                loading={syncingLocal}
                                disabled={syncingLocal}
                                onClick={() => syncLocalDB()}
                                startIcon={<CloudSyncIcon />}
                            >Fatura verilerini eşlemek için tıklayın.

                            </LoadingButton>
                        </Box>
                    </>
                )}


            </Box>
        </>
    )
}

const StyledMenu = styled((props) => (
    <Menu
      elevation={0}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
      {...props}
    />
  ))(({ theme }) => ({
    '& .MuiPaper-root': {
      borderRadius: 6,
      marginTop: theme.spacing(1),
      minWidth: 180,
      color: 'rgb(55, 65, 81)',
      boxShadow:
        'rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px',
      '& .MuiMenu-list': {
        padding: '4px 0',
      },
      '& .MuiMenuItem-root': {
        '& .MuiSvgIcon-root': {
          fontSize: 18,
          color: theme.palette.text.secondary,
          marginRight: theme.spacing(1.5),
        },
        '&:active': {
          backgroundColor: alpha(
            theme.palette.primary.main,
            theme.palette.action.selectedOpacity,
          ),
        },
      },
      ...theme.applyStyles('dark', {
        color: theme.palette.grey[300],
      }),
    },
  }));


function CustomizedMenus(props) {
    const [anchorEl, setAnchorEl] = React.useState(null);
    const open = Boolean(anchorEl);
    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };
    const [syncingGIBHtml, setsyncingGIBHtml] = React.useState(false);
    const [syncingLocal, setsyncingLocal] = React.useState(false);
    const [syncingRemote, setsyncingRemote] = React.useState(false);
    const [syncingALL, setsyncingALL] = React.useState(false);

    const syncAll = async() => {
        let dt = Date.now()
        setsyncingALL(true);
        try {
            await fnSyncGIB();
            console.log('Sunucu veritabanı fatura bilgileri GIB verileri ile eşlendi! sure:' + Date.now() - dt);
            
            await fnsyncGIBHtmls();
            console.log('Sunucu veritabanında fatura dokumanları GIB verileri ile eşlendi!');

            await fnSyncLocalDB();
            console.log('Sunucu veritabanı fatura bilgileri Yerel bilgisayar verileri ile eşlendi!');
            alert ('Fatura eşlemeleri tamamlandı.')
            
            setsyncingALL(false);
            setTimeout(function () {
                window.location.reload(1);
            }, 300);
        } catch (e) {
            setsyncingALL(false);
            alert('hata oldu' + '\n' + JSON.stringify(e))
        }
 
    }
    const fnSyncGIB = async () => {
        return new Promise(async function (resolve, reject) {
            let keysData = {}
            const stickyValue = window.localStorage.getItem(appSettingKey);
            if (stickyValue && stickyValue !== 'null') {
                keysData = (JSON.parse(stickyValue));
            }
            if (keysData?.gibName) {
                let data2Post = {
                    keysData: { ...keysData }
                };
                let uri = '/api/db/fetchinvoices'
                try {
                    // console.log('fnSyncGIB token', props.token)
                    const res = await fetch(uri, {
                        method: 'POST',
                        headers: {
                            'Authorization': 'Bearer ' + props.token,
                            'X-Host': 'Subanet.com',
                        },
                        body: JSON.stringify(data2Post),
                    })

                    if (!res.ok) {
                        var message = `An error has occured: ${res.status} - ${res.statusText}`;
                        console.log(message);
                        reject(message);
                    }

                    const datax = await res.json();
                    if (!datax.error) {
                        console.log('Sunucu veritabanı fatura bilgileri GIB verileri ile eşlendi!');
                        resolve(datax)
                    } else {
                        console.log('err desc', datax);
                        var message = '' + datax.err + (Array.isArray(datax?.messages) ? ('\n' + datax?.messages[0]?.text) : '');
                        reject(message);
                    }
                }
                catch (e) {
                    console.log('e', e)
                    reject(e)
                }
            } else {
                reject('no key')
            } 
        });
    } 
    const syncGIB = async () => {
        try {
            setsyncingRemote(true);
            await fnSyncGIB();
            setsyncingRemote(false);
            alert('Sunucu veritabanı fatura bilgileri GIB verileri ile eşlendi!');
            setTimeout(function () {
                window.location.reload(1);
            }, 300);
        } catch (e) {
            setsyncingRemote(false);
            console.log('error', e)
            alert(e);
        }
    }
    const fnSyncLocalDB = async () => {
        return new Promise(async (resolve, reject) => {
            const url = `/api/db/listinvoicescloud/5000/0`;
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + props.token,
                        'X-Host': 'Subanet.com',
                    },
                });

                if (!response.ok) {
                    reject(false)
                }
                const data = await response.json();
                Array.isArray(data?.data) && data.data.map((d, ix) => {
                    d.id = ix + 1;
                });
                localforage.setItem("invoiceslist", data);
                console.log('Bilgisayarınızdaki fatura verileri güncellendi.');
                resolve(true)

                // if (response.statusText === 'OK') {

                // }
                // else {
                // }
            } catch (error) {
                console.log(error);
                reject(error)
            }
        });
    }
    const syncLocalDB = async () => {
        try {
            setsyncingLocal(true)
            await fnSyncLocalDB();
            setsyncingLocal(false)
            alert('Sunucu veritabanı fatura bilgileri Yerel bilgisayar verileri ile eşlendi!');
            setTimeout(function () {
                window.location.reload(1);
            }, 300);

        } catch (error) {
            setsyncingLocal(false)
            console.log(error);
        }
    }
    const fnsyncGIBHtmls = async () => {
        return new Promise(async (resolve, reject) => {
            let keysData = {}
            const stickyValue = window.localStorage.getItem(appSettingKey);
            if (stickyValue && stickyValue !== 'null') {
                keysData = (JSON.parse(stickyValue));
            }
            if (keysData?.gibName) {
                let data2Post = {
                    keysData: { ...keysData }
                };
                let uri = '/api/db/fetchinvoicehtmls'
                try {
                    const res = await fetch(uri, {
                        method: 'POST',
                        headers: {
                            'Authorization': 'Bearer ' + props.token,
                            'X-Host': 'Subanet.com',
                        },
                        body: JSON.stringify(data2Post),
                    })

                    if (!res.ok) {
                        var message = `An error has occured: ${res.status} - ${res.statusText}`;
                        console.log(message);
                        reject(message);
                    }

                    const datax = await res.json();
                    if (!datax.error) {
                        console.log('Fatura dökümanlarının indirilmesi tamamlandı!');
                        resolve(datax)
                    } else {
                        console.log('err desc', datax);
                        var message = '' + datax.err + (Array.isArray(datax?.messages) ? ('\n' + datax?.messages[0]?.text) : '');
                        reject(message);
                    }

                } catch (e) {
                    console.log('e', e)
                    reject(e)
                }
            } else {
                reject('no key')
            }
        });
    }
    const syncGIBHtmls = async () => {
        try {
            setsyncingGIBHtml(true)
            await fnsyncGIBHtmls();
            setsyncingGIBHtml(false);
            alert('Sunucu veritabanında fatura dokumanları GIB verileri ile eşlendi!');
            setTimeout(function () {
                window.location.reload(1);
            }, 300);
        } catch (e) {
            setsyncingRemote(false);
            console.log('error', e)
            alert(e);
        }
    }

    return (
        <>
            <Button
                id="demo-customized-button"
                aria-controls={open ? 'demo-customized-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={open ? 'true' : undefined}
                //   variant="contained"
                sx={{px: 2}}
                disableElevation
                onClick={handleClick}
                endIcon={<KeyboardArrowDownIcon />}
            >
                Fatura Verilerini Eşitle
            </Button>
            <Menu
                id="demo-customized-menu"
                MenuListProps={{
                    'aria-labelledby': 'demo-customized-button',
                }}
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
            >
                <MenuItem onClick={() => !syncingALL ? syncAll() : true} disableRipple>
                    {syncingALL ? 
                        <Box  sx={{ height: '24px', width: '24px', mr: 2, overflow: 'hidden'}}>
                            <CircularProgress />
                        </Box> : <SyncAltIcon sx={{ mr: 2 }} />}
                    Tümünü Eşitle
                </MenuItem>
                <Divider sx={{ my: 0.5, height: 2, color: 'black', borderBottomWidth: 1, borderColor: 'black' }} />
                
                <MenuItem onClick={() => !syncingLocal ? syncLocalDB() : true} disableRipple>
                    {syncingLocal ? 
                        <Box  sx={{ height: '24px', width: '24px', mr: 2, overflow: 'hidden'}}>
                            <CircularProgress />
                        </Box> : <StorageIcon sx={{ mr: 2 }} />}
                    Yerel Veritabanını Eşitle
                </MenuItem>
                <Divider sx={{ my: 0.5, height: 2, }} />
                <MenuItem onClick={() => !syncingGIBHtml ? syncGIBHtmls() : true} disableRipple>
                    {syncingGIBHtml ? 
                        <Box  sx={{ height: '24px', width: '24px', mr: 2, overflow: 'hidden'}}>
                            <CircularProgress />
                        </Box> : <PictureAsPdfIcon sx={{ mr: 2 }} />}
                    Fatura PDFlerini Eşitle
                </MenuItem>
                <Divider sx={{ my: 0.5 }} />
                <MenuItem onClick={() => !syncingRemote ? syncGIB() : true} disableRipple>
                    {syncingRemote ? 
                        <Box  sx={{ height: '24px', width: '24px', mr: 2, overflow: 'hidden'}}>
                            <CircularProgress />
                        </Box> : <CloudSyncIcon sx={{ mr: 2 }} />}
                    GIB ve Sunucu Eşitle.
                </MenuItem>
                {/* <Divider sx={{ my: 0.5 }} />
                <MenuItem onClick={handleClose} disableRipple>
                    <MoreHorizIcon sx={{ mr: 2 }} />
                    More
                </MenuItem> */}
            </Menu>
        </>
    );
}