import React, { useState, useMemo } from "react";
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme, styled, createTheme, ThemeProvider } from '@mui/material/styles';
export const ColorModeContext = React.createContext({ toggleColorMode: () => { } });

const Provider = props => {
  const theme = useTheme();
  const prefersDarkMode = useMediaQuery('(prefers-color-scheme: dark)');
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.down("md"));
  
  const [mode, setMode] = useState(prefersDarkMode ? 'dark' : 'light');
  const [drawerCollapsed, setdrawerCollapsed] = useState(false);
  const [drawerToggled, setdrawerToggled] = useState(false);
  const [drawerBroken, setdrawerBroken] = useState(false);
  const [currPage, setcurrPage] = useState('/');
  const [currBread, setCurrBread] = useState('/');
  const colorMode = useMemo(
    () => ({
      toggleColorMode: () => {
        setMode((prevMode) => (prevMode === 'light' ? 'dark' : 'light'));
      },
      toggleDrawerCollapse: () => {
        setdrawerCollapsed((prevMode) => (prevMode === true ? false : true));
      },
      collapseDrawer: (val) => {
        setdrawerCollapsed(val === true ? true : false);
      },
      toggleDrawerToggle: () => {
        setdrawerToggled((prevMode) => (prevMode === true ? false : true));
      },
      toggleDrawer: (val) => {
        setdrawerToggled(val === true ? true : false);
      },
      toggleDrawerBroken: () => {
        setdrawerBroken((prevMode) => (prevMode === true ? false : true));
      },
      brokeDrawer: (val) => {
        setdrawerBroken(val === true ? true : false);
      },
      setCurrPage: (val) => {
        setcurrPage(val);
      },
      setCurrBread: (val) => {
        setCurrBread(val);
      },
    }),
    [],
  );

  return (
    <ColorModeContext.Provider
      value={{
        mode,
        setMode: md => setMode(md),
        // toggleColorMode: toggleColorMode,
        colorMode,
        isMobile,
        isTablet,
        drawerCollapsed,
        drawerToggled,
        drawerBroken,
        currPage,
        currBread,
      }}
    >
      {props.children}
    </ColorModeContext.Provider>
  );
};

export default Provider;
