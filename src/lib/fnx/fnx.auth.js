/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

// const fnx = require('./fnx.core');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
// var ObjectId = require('mongodb').ObjectId;
const dbSet = {
    dbName: 'marketplace',
    tables: {
        users: 'auth.users',
        userotps: 'auth.users.otp',
    }
}
 
exports.getUser = ({ dbConn, credentials }) => {
    return new Promise(async (resolve) => {
        const db = dbConn.db(dbSet.dbName)
        const coll = db.collection(dbSet.tables.users);
        const q = {userCode: credentials?.id}; 
        const p = { projection: {_id: 0, password: 0, updatedAt: 0, createdAt: 0, token: 0 } };
        var user = await coll.findOne(q, p); 
        if (user) {
            resolve(user);
        } else {
            resolve({user: false});
        }
    });
}

exports.login = ({ dbConn, credentials }) => {
    return new Promise(async (resolve) => {
        const username = credentials.username ? credentials.username.toLowerCase() : null;
        const db = dbConn.db(dbSet.dbName)
        const coll = db.collection(dbSet.tables.users);
        if (username) {

            const q = { $or: [{ email: username }, { phone: username }] }; //email: username
            const p = { projection: {_id: 0, updatedAt: 0, createdAt: 0 } };
            // var userAll = await coll.aggregate([]).toArray();
            // console.log('p', q)
            var user = await coll.findOne(q, p); 
            // console.log('login q', dbSet.tables.users, q, user, userAll)
            if (user) {
                var hashedPassword = user.password;
                var plainPassword = credentials.password
                bcrypt.compare(plainPassword, hashedPassword, async function (err, res) {
                    delete user.password;
                    if (err || !res) {
                        try {
                            var otpResults = await checkOTP({ user, plainPassword, dbConn })
                            if (!otpResults) resolve({ data: false });
                            if (otpResults) {
                                await deleteOTP({ otpResults, dbConn })
                                delete user.password
                                delete user.password_
                                resolve({ data: user })
                            };
                        } catch (e) {
                            resolve({ data: false });
                        }
                        resolve({ data: false });
                    }
                    delete user.password
                    delete user.password_
                    resolve({data: user});
                });
            } else {
                resolve({data: false});
            }
        } else {
            resolve({data: false, error: 'username / password is needed.'});
        }
    });
}
const deleteOTP = async props => {
    const { otpResults, dbConn } = props
    const db = dbConn.db(dbSet.dbName)
    const coll = db.collection(dbSet.tables.userotps);
    return new Promise(async (resolve, reject) => {
        var o_id = new ObjectId(otpResults?._id);
        try {
            await coll.updateOne({ _id: o_id }, { $set: { eop: new Date() } }, { upsert: true });
            resolve(true);
        } catch (e) {
            console.log('e', e)
            reject(false)
        }
    });
}

const checkOTP = async props => {
    const { user, dbConn, plainPassword } = props;
    const db = dbConn.db(dbSet.dbName)
    const coll = db.collection(dbSet.tables.userotps);

    return new Promise(async (resolve, reject) => {
        var q = { $or: [{ email: user.email }, { phone: user.phone }], eop: { $gte: new Date() }, status: 10 }
        try {
            var userOTP = await coll.findOne(q, {})
            if (userOTP) {
                var { otp } = userOTP;
                bcrypt.compare(plainPassword, otp, async function (err, res) {
                    if (err || !res) reject(false)
                    resolve(userOTP);
                })
            } else {
                reject(false)
            }
        } catch (e) {
            reject(false)
        }
    });
}

exports.getRefreshToken = ({ dbConn, payload }) => {
    const db = dbConn.db(dbSet.dbName)
    const coll = db.collection(dbSet.tables.users);
    const q = {userCode: payload.id}; 
    const p = { projection: {_id: 0, token: 1 } };

    return new Promise(async (resolve, reject) => {
        try {
            const token = await coll.findOne(q, p);
            resolve(token);
        } catch (e) {
            reject(e);
        }
    })
}

exports.saveToken = ({ dbConn, payload, token, saveLogin }) => {
    const db = dbConn.db(dbSet.dbName)
    const coll = db.collection(dbSet.tables.users);
    const q = {email: payload.email, phone: payload.phone, userCode: payload.id}; 

    return new Promise(async (resolve, reject) => {
        try {
            var dataU = {token}
            if (saveLogin) dataU.lastLogin = new Date(Date.now()) 
            await coll.updateOne(q,  { $set: dataU }, { upsert: true } );
            resolve(true);
        } catch (e) {
            reject(e);
        }
    })
}
//https://github.com/TheWidlarzGroup/JWTAuthBackend/blob/main/src/Services/Token.Service.ts
const jwtx = exports.jwtx = {
    sign: (params) => {
        const {payload, lifeTime = 31556926} = params
        const key = process.env.JWT_KEY;
        const options = {
            expiresIn: "10s",
            audience: `${payload.id}`,
        };

        return new Promise(async (resolve, reject) => {
            // console.log('jwtx payload', payload)
            delete payload.exp
            jwt.sign(
                payload,
                key,
                {
                    expiresIn: lifeTime, // 1 year in seconds
                },
                (err, token) => {
                    /* Send succes with token */
                    if(err) reject(err)
                    resolve(token)
                },
            );
        })
    },
}