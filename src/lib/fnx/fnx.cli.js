/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */

exports.camalize = function camalize(str) {
    return str.toLowerCase().replace(/[^a-zA-Z0-9]+(.)/g, (m, chr) => chr.toUpperCase());
}

exports.titlecase_ = function titlecase_(str) {
    return str ? str.toLocaleLowerCase('tr-TR').replace(/\b\w/g, s => s.toLocaleUpperCase('tr-TR')) : '';
  }
  
  
exports.titlecase = function titlecase(s) {
    return s && s !== undefined && s !== null ?  s.replace(/([^\s:\-])([^\s:\-]*)/g,function($0,$1,$2){
        return $1.toLocaleUpperCase('tr-TR')+$2.toLocaleLowerCase('tr-TR');
    }) : null;
}