const path = require('path');
import axios from 'axios';
import moment from 'moment';
import { v1 as uuid } from 'uuid';
import fatura from '@/lib/fnx/gib.invoice.js'
import nodemailer from 'nodemailer';
import { generatePdf, } from 'html-pdf-node';
import AWS from 'aws-sdk';

import { main, logger, sleep, log } from './fnx.core';

const mainTestMode = false;
const dbColls = {
    orders: 'fact_orders',
    ordersMongo: 'uv.fact.orders',
    invoiceTransactionsMongo: 'uv.fact.invoiceTransactions',
    invoicesMongo: 'uv.fact.invoices',
    invoicesHTMLMongo: 'uv.fact.invoicesHTML',
    invoicesEmailedMongo: 'uv.fact.invoicesEmailed',
    invoicesSettingsMongo: 'uv.dim.invoiceSettings',
}

// AWS S3 konfigürasyonu
const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION,
});

// // Nodemailer ile POP3 sunucusuna email gönderme
// var conf = {
//     host: process.env.YANDEX_HOST, // Örneğin: 'pop.mail.server.com'
//     port: process.env.YANDEX_HOST_PORT, // Güvenli bağlantı portu (SSL)
//     secure: true,
//     auth: {
//         user: process.env.YANDEX_EMAIL_ADDRESS,
//         pass: process.env.YANDEX_APP_PASSWORD,
//     },
// }
// // console.log('emailer config', conf)
// const transporter = nodemailer.createTransport(conf);

const generatePdfPromise = async (file) => {
    return new Promise(async (resolve, reject) => {
        let options = { format: 'A4' };
        generatePdf(file, options).then(pdfBuffer => {
            resolve(pdfBuffer)
          });
    });
}

export const app = {
    getSettings: async ({ 
        db,
        userInitials, }) => {
        return new Promise(async (resolve, reject) => {
            var dbCollection = db.collection(dbColls.invoicesSettingsMongo);
            let settings = await dbCollection.aggregate([{$match: {deleted: false}}]).toArray();
            settings = settings ? settings[0] : false;
            resolve({
                dt: new Date(),
                settings,
            })
        });
    },
    setSettings: async ({ 
        db,
        modeDebug = false, refTime = null,
        reqQuery, reqBody,
        userInitials, }) => {
        return new Promise(async (resolve, reject) => {

            var query = reqQuery;
            var body = reqBody;
            var getResp = { query: query, body }

            if (body) {
                let emailData = JSON.parse(body);
                var dbCollection = db.collection(dbColls.invoicesSettingsMongo);
                let settings = await dbCollection.aggregate([{$match: {deleted: false}}]).toArray();
                settings = settings ? settings[0] : false;
                
                var {invEmailBCC, invEmailBody, invEmailSubject} = emailData;
                var u = {
                    invEmailBCC, invEmailBody, invEmailSubject
                }
                await dbCollection.updateOne({ _id: settings?._id }, { $set: { dtUpdated: new Date(), ...u } }, { upsert: true });
                
                resolve({
                    ...emailData,
                    dt: new Date(),
                    settings,
                    userInitials,
                })
            } else {
                resolve({
                    error: true,
                    dt: new Date(),
                    userInitials,
                })
            }
        });
    },
}
export const gib = {
    fnLogout: async ({ token }) => {
        return new Promise(async (resolve, reject) => {
            try {
                const act = await fatura.logout(token);
                console.log('logged out');
                resolve(true);
            } catch (e) {
                console.log('log out act failed', e);
                resolve(false)
            }
        });
    },
    fnLogin: async ({ testMode = false, username, password }) => {
        return new Promise(async (resolve, reject) => {
            let resp = {}
            try {
                testMode && fatura.enableTestMode();
                const token = await fatura.getToken(username, password);
                if (token?.error) {
                    console.log('login hatası', testMode, username, password, token)
                    resp.code = 0;
                    resp.err = 'Oturum acilamadi.';
                    resp = {
                        ...resp,
                        ...token,
                    }
                    resolve(resp);
                } else {
                    resolve({token})
                }
            }
            catch (e) {
                reject(false)
            }
        });
    },
    _listInvoices: async ({ db, storeName = 'uniqeravintage', gibaccount = '********', dir = 'data', modeDebug = false, reqQuery, userInitials }) => {
        const { slug, sortBy = 'id', sortType = -1, fields } = reqQuery
        const limit = Array.isArray(slug) && slug[1] ? slug[1] : 15
        const offset = Array.isArray(slug) && slug[2] ? slug[2] : 0

        var bop = Date.now();
        return new Promise(async function (resolve, reject) {
            var dbCollection = db.collection(dbColls.invoicesMongo);
            let data = [];
            let qBase = [
                { "$match": { "account": storeName, "gibaccount": gibaccount } },
                { "$sort": { "belgeNumarasi": -1, } },
                { "$lookup": {
                        from: dbColls.invoicesHTMLMongo, // "cust.invoices",
                        let: { belgeNumarasi: "$belgeNumarasi", account: "$account", gibaccount: "$gibaccount" },
                        pipeline: [
                            {
                                $match: {
                                    $and: [
                                        { $expr: { $eq: ["$belgeNumarasi", "$$belgeNumarasi"] } },
                                        { $expr: { $eq: ["$account", "$$account"] } },
                                        { $expr: { $eq: ["$gibaccount", "$$gibaccount"] } },
                                    ]
                                }
                            },
                            { "$project": { belgeNumarasi: 1 } },
                        ],
                        as: "invoiceHTMLs",
                    }
                },

                {
                    "$lookup": {
                        from: dbColls.invoicesEmailedMongo, // "cust.invoices",
                        let: { belgeNumarasi: "$belgeNumarasi", account: "$account", gibaccount: "$gibaccount" },
                        pipeline: [
                            {
                                $match: {
                                    $and: [
                                        { $expr: { $eq: ["$belgeNumarasi", "$$belgeNumarasi"] } },
                                        { $expr: { $eq: ["$account", "$$account"] } },
                                        { $expr: { $eq: ["$gibaccount", "$$gibaccount"] } },
                                    ]
                                }
                            },
                            { "$project": { belgeNumarasi: 1 } },
                        ],
                        as: "emails",
                    }
                },

                { "$addFields": {
                        htmlDocs: { $size: "$invoiceHTMLs" }
                    },
                },
                {
                    "$addFields": {
                        emails: { $size: "$emails" }
                    },
                },
                { "$project": { _id: 0, invoiceHTMLs: 0 } },

            ];
            // var qProject = { _id: 0, }
            // qBase.push({ "$project": qProject });
            let recCountStg;
            recCountStg = await dbCollection.aggregate([...qBase, { $count: "records" }]).toArray()
            let recCount = recCountStg && Array.isArray(recCountStg) && recCountStg.length !== 0 ? recCountStg[0]?.records : 0;

            var qSort = {}
            if (sortBy) {
                qSort[sortBy] = sortType == 1 ? 1 : -1
                qBase.push({ "$sort": qSort });
            }
            offset && qBase.push({ $skip: parseFloat(offset) })
            limit && qBase.push({ $limit: parseFloat(limit) })


            data = await dbCollection.aggregate([...qBase]).toArray()

            const totalPages = Math.ceil(recCount / limit);
            const currentPage = Math.ceil(offset / recCount * totalPages) ? (Math.ceil(offset / recCount * totalPages)) : 1;
            
            // log('qBase invoices', JSON.stringify(qBase))
            // modeDebug && logger.info('recCount', recCount)
            // modeDebug && logger.info('currentPage', currentPage)
            // modeDebug && logger.info('totalPages', totalPages)
            // modeDebug && logger.info('listOrders', limit, offset)
            resolve({
                data: data,
                paging: {
                    total: recCount,
                    page: currentPage,
                    pages: totalPages,
                },
            });
        });
    },
    _listInvoice: async ({ db, storeName = 'uniqeravintage', gibaccount = '********', dir = 'data', modeDebug = false, reqQuery, userInitials }) => {
        const { slug, sortBy = 'id', sortType = -1, fields } = reqQuery
        const ettn = Array.isArray(slug) && slug[1] ? slug[1] : false
        var bop = Date.now();
        return new Promise(async function (resolve, reject) {
            var dbCollection = db.collection(dbColls.invoicesMongo);
            let data = [];
            let qBase = [
                { "$match": { "account": storeName, "gibaccount": gibaccount, ettn } },
                //{ "$sort": { "belgeNumarasi": -1, } },
                { "$lookup": {
                        from: dbColls.invoicesHTMLMongo, // "cust.invoices",
                        let: { belgeNumarasi: "$belgeNumarasi", account: "$account", gibaccount: "$gibaccount" },
                        pipeline: [
                            {
                                $match: {
                                    $and: [
                                        { $expr: { $eq: ["$belgeNumarasi", "$$belgeNumarasi"] } },
                                        { $expr: { $eq: ["$account", "$$account"] } },
                                        { $expr: { $eq: ["$gibaccount", "$$gibaccount"] } },
                                    ]
                                }
                            },
                            { "$project": { belgeNumarasi: 1 } },
                        ],
                        as: "invoiceHTMLs",
                    }
                },

                {
                    "$lookup": {
                        from: dbColls.invoicesEmailedMongo, // "cust.invoices",
                        let: { belgeNumarasi: "$belgeNumarasi", account: "$account", gibaccount: "$gibaccount" },
                        pipeline: [
                            {
                                $match: {
                                    $and: [
                                        { $expr: { $eq: ["$belgeNumarasi", "$$belgeNumarasi"] } },
                                        { $expr: { $eq: ["$account", "$$account"] } },
                                        { $expr: { $eq: ["$gibaccount", "$$gibaccount"] } },
                                    ]
                                }
                            },
                            { "$project": { belgeNumarasi: 1 } },
                        ],
                        as: "emails",
                    }
                },

                { "$addFields": {
                        htmlDocs: { $size: "$invoiceHTMLs" }
                    },
                },
                {
                    "$addFields": {
                        emails: { $size: "$emails" }
                    },
                },
                { "$project": { _id: 0, invoiceHTMLs: 0 } },

            ];
            // console.log('_listInvoice', JSON.stringify(qBase))
            data = ettn && await dbCollection.aggregate([...qBase]).toArray()
            resolve({
                data: data ? data[0] : false,
            });
        });
    },
    fnGetGIBInvoices: async ({ db, login, testMode, startDate, endDate, storeName, modeDebug }) => {
        return new Promise(async (resolve, reject) => {
            try {
                startDate = startDate || moment().subtract(3315, 'day').format('DD/MM/YYYY');
                endDate = endDate || moment().format('DD/MM/YYYY');
                // console.log('login', login);
                testMode && fatura.enableTestMode();
                console.log('login act...')
                let tokenArct = await gib.fnLogin({ ...login, testMode })
                if (tokenArct.token) {
                    let token = tokenArct.token;
                    console.log('logged in. fetching.. start end Date!', { startDate, endDate });
                    let invAct = {}
                    try {
                        invAct = await fatura.getAllInvoicesByDateRange(token, { startDate, endDate });
                        try {
                            const filePath = path.resolve(process.cwd(), './src/lib/bins/' + login.username + '_invoices' + Date.now().toString() + '.json');
                            await main.save2file({ data: invAct, filePath, modeDebug: false });
                            console.log('saved json file.', invAct.length, 'records.')
                        } catch (eSave) {
                            // console.log('save file error', eSave);
                        }
                        try {
                            let data2Remove = [...invAct].map(d => d.ettn)
                            let removeQ = { ettn: { $in: data2Remove } };
                            await db.collection(dbColls.invoicesMongo).deleteMany(removeQ) //.exec()
                            // console.log('invoices 2 add deleted if exists in currDB');

                            let rown = 0;
                            let data2Save = []
                            for (let d of invAct) {
                                rown++;
                                d.lastSourceSyncAt = new Date(Date.now());
                                d.account = storeName;
                                d.gibaccount = login.username;
                                data2Save.push(d);
                            };
                            const options = { ordered: true };
                            await db.collection(dbColls.invoicesMongo).insertMany(data2Save, options)
                            modeDebug && log('invoice DB records', data2Save.length);
                        }
                        catch (eDB) {
                            console.log('save2db error', eDB)
                        }
                        await gib.fnLogout({token})
                        resolve({ token })
                    } catch (ePostToken) {
                        console.log('error in fetching...', ePostToken)
                        await gib.fnLogout({token})
                        reject({
                            "error": "2",
                            "code": 2,
                            "err": "Oturum acildi ancak fatura listesi oluşturulamadı.",
                            "messages": [
                                {
                                    "type": "X5",
                                    "text": "fatura listesi alınamadı.!"
                                }
                            ],
                            ...invAct,
                        })
                    }
                } else {
                    console.log('login failed!', tokenArct);
                    reject(tokenArct)
                }
            } catch (e) {
                reject(e)
            }
        });
    },
    fetchInvoices: async ({ db, storeName = 'uniqeravintage',
        modeDebug = false, refTime = null,
        reqQuery, reqBody, fetchTransactions = false }) => {
        var bop = Date.now();
        return new Promise(async function (resolve, reject) {
            try {
                var query = reqQuery;
                var body = reqBody;
                var getResp = { query: query, body }
                if (body) {
                    let invoiceData = JSON.parse(body);
                    let keysData = invoiceData?.keysData || {};
                    let testMode = keysData?.gibTest || mainTestMode;

                    let startDate = moment().subtract(testMode ? 10 : 3000, 'day').format('DD/MM/YYYY');
                    let endDate = moment().subtract(-1, 'day').format('DD/MM/YYYY');

                    if (testMode && !keysData.gibName) {
                        keysData.gibName = '********';
                        keysData.gibPass = '1';
                    }
                    let act = //true; 
                    await gib.fnGetGIBInvoices({
                        db, storeName,
                        login: {
                            username: keysData.gibName, //********
                            password: keysData.gibPass, //853241
                        },
                        startDate, endDate,
                        testMode, modeDebug
                    });
                    // console.log('data2Process get:', invoiceData, startDate, endDate, act)
                    // let data = preOrderData([dataStg]); 
                    getResp.invoices = { ...act };
                } else {
                    getResp.error = 'no Body sent from client!'
                }
                resolve({ data: { ...getResp }, status: 200, sure: Date.now() - bop })
            } catch (e) {
                console.log('fetch failed', e)
                reject(e)
            }
        });
    },
    fetchInvoiceHTMLs: async ({ db, storeName = 'uniqeravintage', gibaccount = '********', token,
        modeDebug = false, reqQuery, reqBody }) => {
        var bop = Date.now();
        const fetchLimit = 610;
        return new Promise(async function (resolve, reject) {
            try {
                var query = reqQuery;
                var body = reqBody;
                var getResp = { query: query, body };

                let refData = JSON.parse(body);

                let keysData = refData?.keysData || {};
                let testMode = keysData?.gibTest || mainTestMode;
                if (testMode && !keysData.gibName) {
                    keysData.gibName = '********';
                    keysData.gibPass = '1';
                }
                let login = {
                    username: keysData.gibName, //********
                    password: keysData.gibPass, //853241
                }
                testMode && fatura.enableTestMode();
                getResp.login = login;

                let qMainRef = [
                    {$match: {"onayDurumu": "Onaylandı", "account": storeName, "gibaccount": gibaccount}},
                    {$project: {_id: 0, belgeNumarasi: 1, ettn: 1}},
                    {$sort: {"belgeNumarasi": -1}},
                ];
                var dbCollection = db.collection(dbColls.invoicesMongo);
                var dbHTMLCollection = db.collection(dbColls.invoicesHTMLMongo);
                let OnaylanmisFaturalar = await dbCollection.aggregate([...qMainRef]).toArray();
                // OnaylanmisFaturalar = Array.isArray(OnaylanmisFaturalar) && OnaylanmisFaturalar.map(o => o.belgeNumarasi);
                let qRefHTML = [
                    {$match: {"account": storeName, "gibaccount": gibaccount}},
                    {$project: {_id: 0, belgeNumarasi: 1}},
                ];
                let IndirilmisFaturalar = await dbHTMLCollection.aggregate([...qRefHTML]).toArray();
                IndirilmisFaturalar = Array.isArray(IndirilmisFaturalar) && IndirilmisFaturalar.map(o => o.belgeNumarasi);

                let indirilecekFaturalar = Array.isArray(OnaylanmisFaturalar) && OnaylanmisFaturalar.filter(i => !IndirilmisFaturalar.includes(i.belgeNumarasi)).slice(0, fetchLimit)
                // GIB ACTS
                // GIB ACTS
                let tokenArct = await gib.fnLogin({ ...login, testMode })
                if (tokenArct.token) {
                    let token = tokenArct.token;
                    for (const inx of indirilecekFaturalar) {
                        try {
                            let html = await fatura.getInvoiceHTML2(token, inx.ettn, "Onaylandı");
                            let d2Post = {
                                ettn: inx.ettn,
                                belgeNumarasi: inx?.belgeNumarasi,
                                account: storeName,
                                gibaccount,
                                invoiceHTML: html,
                                dtCreated: new Date(Date.now()),
                            }
                            const result = await dbHTMLCollection.insertOne(d2Post);
                            console.log('result', token, inx.belgeNumarasi)
                        } catch (eF) {
                            console.log('fetch Error', token, eF, inx.belgeNumarasi);
                        }
                    }
                    await gib.fnLogout({ token });
                } else {
                    console.log('login failed!', tokenArct, login, testMode, refData);
                    getResp.error = true;
                    getResp.errorDesc = 'login failed';
                    getResp = { ...getResp, ...tokenArct}
                }
                // GIB ACTS
                // GIB ACTS
                getResp.arrays = {
                    OnaylanmisFaturalar,
                    IndirilmisFaturalar,
                    delta: ['etc...'],
                    items2Fetch: indirilecekFaturalar,
                    sure: Date.now() - bop
                }
                resolve(getResp)
                /*
                akış:
                onaylanmış olan faturaları gib inv. tablosundan çek.
                mevcuttaki html tablosundaki inv. tablosunu cek.
                deltayı indir... ve tabloya yaz...
                */
            } catch (e) {
                console.log('e fetchInvoiceHTMLs', e)
                reject(e)
            }
        })
    },
    sendinvoiceEmail: async ({ db, storeName = 'uniqeravintage', gibaccount = '********', token,
        modeDebug = false, reqQuery, reqBody }) => {
        var bop = Date.now();
        return new Promise(async function (resolve, reject) {
            try {
                var query = reqQuery;
                var body = reqBody;
                var getResp = { query: query, body };
                let refData = JSON.parse(body);
                let invNo = refData.belgeNumarasi || refData.uuid 
                getResp.invNo = invNo;

                var dbCollection = db.collection(dbColls.invoicesHTMLMongo);
                let qMainRef = [
                    { $match: { $or: [{belgeNumarasi: invNo}, {ettn: invNo}], account: storeName } },
                ]
                let FaturaHTMLData = await dbCollection.aggregate([...qMainRef]).toArray();
                FaturaHTMLData = Array.isArray(FaturaHTMLData) && FaturaHTMLData.length !== 0 ? FaturaHTMLData[0] : false;
                if (FaturaHTMLData) {
                    let htmlContent = FaturaHTMLData?.invoiceHTML;
                    try {
                        const file = { content: htmlContent };
                        const pdfBuffer = await generatePdfPromise(file);
                        const pdfFileName = `gib-${FaturaHTMLData.ettn}.pdf`;
                        // console.log('process.env', process.env)
                        // S3 Yükleme Parametreleri
                        const uploadParams = {
                            Bucket: process.env.AWS_S3_BUCKET_NAME, // S3 bucket adı
                            Key: pdfFileName, // Dosya adı
                            Body: pdfBuffer, // Yüklenecek veri (PDF)
                            ContentType: 'application/pdf', // İçerik tipi
                        };

                        // PDF'i S3'e yükleme
                        const s3Response = await s3.upload(uploadParams).promise();

                        // Yüklenen dosyanın S3 URL'si
                        const pdfUrl = s3Response.Location;

                        var {aliciAdi, epostaAdresi} = refData;
                        let emailA = '<EMAIL>'


                        var dbCollectionS = db.collection(dbColls.invoicesSettingsMongo);

                        let settings = await dbCollectionS.aggregate([{ $match: { deleted: false } }]).toArray();
                        settings = settings ? settings[0] : false;
                        var { invEmailBCC, invEmailBody, invEmailSubject } = settings;

                        const repl = {
                            aliciAdi: aliciAdi,
                            pdfUrl: pdfUrl,
                            logo: "cid:logo1",
                          }
                        const replace = (string, object) => string.replaceAll(/\{([^}]+)\}/gi, (_, a) => a.split('.').reduce((b, c) => b?.[c], object));
                        var mBody = replace(invEmailBody, repl);
                        var mTextBody = mBody.replace(/<[^>]*>?/gm, '');
                        var mailOptions = {
                            from: '<EMAIL>',
                            to: emailA,
                            bcc: '<EMAIL>', // Gizli alıcı buraya ekleniyor
                            subject: invEmailSubject || 'Uniqera E-arşiv Faturası',
                            html: mBody,
                            text: mTextBody,
                            textOrj: `Sevgili ${aliciAdi}, siparişiniz için teşekkür ederiz. Temin edilen ürünlerinize ait e-arşiv faturanızı görüntüleyebilirsiniz. Aynı zamanda aşağıdaki web adresinden istediğiniz zaman erişebilirsiniz. \n\n${pdfUrl}`,
                            htmlOrj: ` <h2 style="margin-bottom: 20px;">Sevgili ${aliciAdi}, siparişiniz i&ccedil;in teşekk&uuml;r ederiz.</h2>
                                    <p>E-arşiv faturanızı ekte g&ouml;r&uuml;nt&uuml;leyebilir ya da aşağıdaki linkten ulaşabilirsiniz.</p>
                                    <div style="margin-top: 5px; font-family: sans-serif; font-size: 12px; line-height: 25px; color: #666666;"><strong>link:</strong> ${pdfUrl}</div>
                                    <div style="margin-top: 5px; font-family: sans-serif; font-size: 12px; line-height: 25px; color: #666666;"><br /> <a style="color: #298eea; text-decoration: none; font-size: 13px;" href="https://uniqeravintage.com" target="_blank" rel="noopener" data-saferedirecturl="https://www.google.com/url?q=https://uniqeravintage.com&amp;source=gmail"> <img src="cid:logo1" /> </a> <br /> </div>
                            `,
                            attachments: [
                                {
                                    filename: pdfFileName,
                                    content: pdfBuffer,
                                    contentType: 'application/pdf',
                                },
                                {
                                    filename: 'logo.png',
                                    path: path.resolve(process.cwd(), './public/logo.png'),
                                    cid: 'logo1' //same cid value as in the html img src
                                }
                            ],
                        };
                        mailOptions.bcc += invEmailBCC ? (', ' + invEmailBCC) : ''
                        // const xmailOptions = {
                        //     from: '<EMAIL>',
                        //     to: emailA,
                        //     bcc: '<EMAIL>', // Gizli alıcı buraya ekleniyor
                        //     subject: 'Uniqera E-arşiv Faturası',
                        //     text: `Sevgili ${aliciAdi}, siparişiniz için teşekkür ederiz. Temin edilen ürünlerinize ait e-arşiv faturanızı görüntüleyebilirsiniz. Aynı zamanda aşağıdaki web adresinden istediğiniz zaman erişebilirsiniz. \n\n${pdfUrl}`,
                        //     html: ` <h2 style="margin-bottom: 20px;">Sevgili ${aliciAdi}, siparişiniz i&ccedil;in teşekk&uuml;r ederiz.</h2>
                        //             <p>E-arşiv faturanızı ekte g&ouml;r&uuml;nt&uuml;leyebilir ya da aşağıdaki linkten ulaşabilirsiniz.</p>
                        //             <div style="margin-top: 5px; font-family: sans-serif; font-size: 12px; line-height: 25px; color: #666666;"><strong>link:</strong> ${pdfUrl}</div>
                        //             <div style="margin-top: 5px; font-family: sans-serif; font-size: 12px; line-height: 25px; color: #666666;"><br /> <a style="color: #298eea; text-decoration: none; font-size: 13px;" href="https://uniqeravintage.com" target="_blank" rel="noopener" data-saferedirecturl="https://www.google.com/url?q=https://uniqeravintage.com&amp;source=gmail"> <img src="cid:logo1" /> </a> <br /> </div>
                        //     `,
                        //     xhtml: `<h2 style="margin-bottom:20px">Sevgili ${aliciAdi}, siparişiniz için teşekkür ederiz.</h2>E-arşiv faturanızı ekte görüntüleyebilirsiniz ya da aşağıdaki linkten ulaşabilirsiniz. 
                        //             <br>         
                        //             <br>   
                        //             <div style="margin-top:5px;font-family:sans-serif;font-size:12px;line-height:25px;color:#666666">
                        //                 <strong>link:</strong> ${pdfUrl}
                        //             </div>          
                        //             <br>  
                        //             <br>
                        //             <a href="https://uniqeravintage.com" target="_blank" data-saferedirecturl="https://www.google.com/url?q=https://uniqeravintage.com&source=gmail" style="color:#298eea;text-decoration:none;font-size:13px" >
                        //                 <img src="cid:logo1"/>
                        //             </a>
                        //             <br>
                        //             <br>
    
                        //     `,
                        //     attachments: [
                        //         {
                        //             filename: pdfFileName,
                        //             content: pdfBuffer,
                        //             contentType: 'application/pdf',
                        //         },
                        //         {
                        //             filename: 'logo.png',
                        //             path: path.resolve(process.cwd(), './public/logo.png'),
                        //             cid: 'logo1' //same cid value as in the html img src
                        //         }
                        //     ],
                        // };

                        // Nodemailer ile POP3 sunucusuna email gönderme
                        var conf = {
                            host: process.env.YANDEX_HOST, // Örneğin: 'pop.mail.server.com'
                            port: process.env.YANDEX_HOST_PORT, // Güvenli bağlantı portu (SSL)
                            secure: true,
                            auth: {
                                user: process.env.YANDEX_EMAIL_ADDRESS,
                                pass: process.env.YANDEX_APP_PASSWORD,
                            },
                        }
                        const transporter = nodemailer.createTransport(conf);
                        await transporter.sendMail(mailOptions);

                        //save 2 db..  
                        let d2Post2DB = {
                            ettn: FaturaHTMLData.ettn,
                            belgeNumarasi: FaturaHTMLData.belgeNumarasi,
                            account: FaturaHTMLData.account,
                            gibaccount: FaturaHTMLData.gibaccount,
                            from: mailOptions.from,
                            to: mailOptions.to,
                            subject: mailOptions.subject,
                            html: mailOptions.html,
                            pdfUrl: pdfUrl,
                            aliciAdi: refData.aliciAdi,
                            epostaAdresi: refData.epostaAdresi, 
                            dtCreated: new Date(Date.now()),
                        }
                        var dbCollLog = db.collection(dbColls.invoicesEmailedMongo);
                        await dbCollLog.insertOne(d2Post2DB);

                        console.log('email sent', emailA)
      
                        getResp.pdfUrl = pdfUrl;

                    } catch (ePdf) {
                        getResp.error = true;
                        getResp.errorDesc = 'PDF Oluşturulamadı';
                        console.log('error PDF', ePdf);
                    }
                    getResp.resp = FaturaHTMLData;
                } else {
                    getResp.error = true;
                    getResp.errorDesc = 'Fatura bilgisine ulaşılamadı.'
                }
                // getResp.q = qMainRef;
                resolve(getResp)
            } catch (e) {
                console.log('e sendinvoiceEmail', e)
                reject(e)
            }
        })
    },
    getEmailedInvoices: async({ db, reqQuery = {}, }) => {
        return new Promise(async function (resolve, reject) {
            let {slug} = reqQuery;
            let ettn = Array.isArray(slug) && slug[1];
            var dbCollLog = db.collection(dbColls.invoicesEmailedMongo);
            let emails = await dbCollLog.aggregate([
                { $match: { ettn } }, 
                { $sort: { dtCreated: -1}}
            ]).toArray();
            let pdf = Array.isArray(emails) && emails.length !== 0 && emails[0]?.pdfUrl;
            resolve({ data: {
                pdf,
                emails,
            }})
        });
    },
    getInvoiceHTML: async ({ db, storeName = 'uniqeravintage', gibaccount = '********', reqQuery = {} }) => {
        let dtBOP = Date.now();
        return new Promise(async function (resolve, reject) {
            let {slug} = reqQuery;
            let invNo = Array.isArray(slug) && slug[1];
            if (!invNo) {
                resolve({error: true, errorDesc: 'invoice ID needed'});
            } else {
                // console.log('reqQuery', reqQuery, invNo)
                let qMainRef = [
                    // { $match: { belgeNumarasi: invNo, account: storeName } },
                    { $match: { $or: [{belgeNumarasi: invNo}, {ettn: invNo}], account: storeName } },
                    { $lookup: {
                            from: dbColls.invoiceTransactionsMongo, // "cust.invoices",
                            let: { ettn: "$ettn", account: "$account", gibaccount: "$gibaccount" },
                            pipeline: [
                                {
                                    $match: {
                                        $and: [
                                            { $expr: { $eq: ["$uuid", "$$ettn"] } },
                                            { $expr: { $eq: ["$account", "$$account"] } },
                                            // {$expr: { $eq: ["$gibaccount", "$$gibaccount"]}}, 
                                        ]
                                    }
                                }, //, "invoiceStatus": 1
                                { "$project": { _id: 0 } },
                            ],
                            as: "orderData",
                        }
                    },
                    { $addFields: { orderData: { $first: "$orderData" } } },
                    { $limit: 1 }
                ];

                var dbCollection = db.collection(dbColls.invoicesHTMLMongo);
                let FaturaHTML = await dbCollection.aggregate([...qMainRef]).toArray();
                FaturaHTML = Array.isArray(FaturaHTML) && FaturaHTML.length !== 0 ? FaturaHTML[0] : false
                if (FaturaHTML) {
                    FaturaHTML.onayDurumu = 'Onaylandi'
                }
                // console.log('query', JSON.stringify(qMainRef));
                // console.log('resp', FaturaHTML);
                if (!FaturaHTML) {
                    var dbCollectionINV = db.collection(dbColls.invoiceTransactionsMongo);
                    let q = [
                        { $match: { $or: [{ "draftInvoiceDetails.belgeNumarasi": invNo }, { "draftInvoiceDetails.ettn": invNo }], account: storeName } },
                        { $addFields: { orderData: "$refOrderData" } },
                        { $addFields: {"invoiceHTML":"$draftInvoiceHTML"}},
                        {"$addFields":{"orderData.orderid":"$orderData.id"}},
                        { $project: { refOrderData: 0, draftInvoiceHTML: 0 } },
                        { $limit: 1 }
                    ]
                    // console.log('qqqq', JSON.stringify(q));
                    FaturaHTML = await dbCollectionINV.aggregate([...q]).toArray();
                    FaturaHTML = Array.isArray(FaturaHTML) && FaturaHTML.length !== 0 ? FaturaHTML[0] : false
                    if (FaturaHTML) {
                        FaturaHTML.onayDurumu = 'Onaylanmadı'
                    }
                }
                resolve({data: invNo, html: FaturaHTML})
            }
            
        });
    },
    fnCreateGIBInvoice: async ({ db, storeName = 'uniqeravintage', data2Post, refData, modeDebug = false }) => {
        let dtBOP = Date.now();
        return new Promise(async function (resolve, reject) {
            let resp = {}
            try {
                var dbCollection = db.collection(dbColls.invoiceTransactionsMongo);
                let keysData = refData?.keysData || {};
                let testMode = keysData?.gibTest || mainTestMode;
                if (testMode && !keysData.gibName) {
                    keysData.gibName = '********';
                    keysData.gibPass = '1';
                }
                let login = {
                    username: keysData.gibName, //********
                    password: keysData.gibPass, //853241
                }
                testMode && fatura.enableTestMode();
                if (testMode) {
                    data2Post.name = "İsim Soyİsim";
                    data2Post.email = '<EMAIL>';
                    data2Post.phoneNumber = '901234567890';
                    data2Post.fullAddress = 'Erenkoy mah. Eren sok. Eren apt. No:23 kat:1 daire:5 Kozyatagi Kadikoy'
                }
                console.log('login act...', {testMode}, Date.now() - dtBOP)
                let tokenArct = await gib.fnLogin({ ...login, testMode })
                if (tokenArct.token) {
                    let token = tokenArct.token;
                    resp.token = token;
                    resp.data2Post = data2Post;
                    console.log('logged in. fetching..', { token }, Date.now() - dtBOP);
                    try {
                        let postData2DB = {
                            orderid: refData?.refOrderData.id,
                            invoiceTransactionID: refData.id,
                            account: storeName,
                            marketID: createMarketID(storeName, data2Post),
                            uuid: data2Post.uuid,
                            invoiceStatus: 1, //1 preDraft, //2 draft, //3 signed. //4 sent!
                            islemSaati: new Date(),
                            refOrderData: refData?.refOrderData,
                            invData: data2Post,
                            client: 'shopify-web-shopivo',
                            gibaccount: keysData.gibName,
                        };

                        const faturaHTML =  //false; 
                            await fatura.createInvoiceAndGetHTML(
                                login.username,
                                login.password,
                                data2Post,
                                { sign: false } // Varsayılan olarak sign: true gönderilir.
                            );
                        if (faturaHTML) {
                            postData2DB.draftInvoiceDetails = faturaHTML?.draftInvoiceDetails
                            postData2DB.draftInvoiceHTML = faturaHTML?.draftInvoiceHTML;
                            postData2DB.dtDraftInvoiceHTML = new Date();
                            const result = await dbCollection.insertOne(postData2DB);
                            // console.log(
                            //     `A document was inserted with the _id: ${result.insertedId}`,
                            //  );
                            resp = {
                                ...resp,
                                postData2DB: {...postData2DB},
                                ...faturaHTML,
                            }

                            resp.code = 200;
                            // result.draftInvoice = faturaHTML?.draftInvoice;
                            // result.draftInvoiceDetails = faturaHTML.draftInvoiceDetails;
                            resp.sure = Date.now() - dtBOP;
                            await gib.fnLogout({ token });
                            // console.log('resp', JSON.stringify(resp))
                            resolve({ ...resp, success: true, });
                        } else {
                            await gib.fnLogout({ token })
                            resp.sure = Date.now() - dtBOP;
                            resolve({ ...resp, success: false, error: true, faturaHTML,  });
                        }

                    } catch (eSave) {
                        console.log('save file error', eSave);
                        resolve({ ...resp, success: false, error: true, errorSystem: JSON.stringify(eSave),  });
                    }
                    // resolve(data2Post)
                } else {
                    console.log('login failed!', tokenArct, login, testMode, refData);
                    resp.success = false;
                    resp.error = true;
                    resp.errorDesc = 'login failed'
                    resp = {
                        ...resp,
                        ...tokenArct,
                    };
                    resolve(resp)
                    // resp.errorRef = tokenArct
                    // reject(tokenArct)
                }
            } catch (e) {
                resp.success = false;
                resp.error = true;
                resp.errorResp = JSON.stringify(e);
                resp.errorDesc = 'check server logs';
                console.log('fnCreateGIBInvoice err:', e)
                resolve(resp)
            }
        });
    },
    createGIBInvoice: async ({
        db, storeName = 'uniqeravintage', gibaccount = '********', token,
        modeDebug = false, reqQuery, reqBody
    }) => {
        var bop = Date.now();
        return new Promise(async function (resolve, reject) {
            try {
                var query = reqQuery;
                var body = reqBody;
                var getResp = { query: query, body };

                if (body) {
                    let invoiceData = JSON.parse(body);
                    // let keysData = invoiceData?.keysData || {};
                    // let testMode = keysData?.gibTest || mainTestMode;

                    let data2Process = { ...invoiceData };
                    let newItems = Array.isArray(data2Process.item) && data2Process.item.map(d => {
                        return ({
                            name: d.name,
                            quantity: parseFloat(d.quantity),
                            unitPrice: parseFloat(parseFloat(d.unitPrice).toFixed(2)),
                            price: parseFloat(parseFloat(d.price).toFixed(2)),
                            VATRate: parseFloat(d.VATRate) * 100,
                            VATAmount: parseFloat(parseFloat(d.VATAmount).toFixed(2)),
                        })
                    });
                    
                    data2Process.date = moment(new Date(data2Process.date)).format('DD/MM/YYYY').toString();
                    data2Process.time += ':00';
                    data2Process.title = '';
                    data2Process.surname = '';
                    data2Process.district = data2Process.district || '';
                    
                    data2Process.phoneNumber = data2Process.phoneNumber || '';
                    data2Process.phoneNumber = data2Process.phoneNumber.replace(/\D/g,'');

                    data2Process.totalVAT =  parseFloat(parseFloat(data2Process.totalVAT).toFixed(2)),
                    data2Process.grandTotal =  parseFloat(parseFloat(data2Process.grandTotal).toFixed(2)),
                    data2Process.grandTotalInclVAT =  parseFloat(parseFloat(data2Process.grandTotalInclVAT).toFixed(2)),
                    data2Process.paymentTotal =  parseFloat(parseFloat(data2Process.paymentTotal).toFixed(2)),

                    data2Process.item = newItems;
                    data2Process.uuid = uuid();
                    delete data2Process.checks;
                    delete data2Process.dateCreated;
                    delete data2Process.refOrderData;
                    delete data2Process.sumDiscountAmount;
                    // console.log('d2Post', JSON.stringify(data2Process));

                    let invData = createInvoiceInitials(data2Process); //testMode

                    let act = await gib.fnCreateGIBInvoice({
                        db,
                        storeName,
                        data2Post: invData,
                        refData: invoiceData,
                        modeDebug,
                    })
                    // // console.log('d2Post', JSON.stringify(act));
                    getResp.createInvoice = {...act, invoiceData}
                } else {
                    getResp.error = 'no Body sent from client!'
                }
                resolve(getResp)
            } catch (e) {
                console.log('e createGIBInvoice', e)
                reject(e)
            }
        });
    },
}
export const shopify = {
    _listOrders: async ({ db, storeName = 'uniqeravintage', dir = 'data', modeDebug = false, reqQuery, userInitials }) => {
        const { slug, sortBy = 'id', sortType = -1, fields } = reqQuery
        const limit = Array.isArray(slug) && slug[1] ? slug[1] : 15
        const offset = Array.isArray(slug) && slug[2] ? slug[2] : 0

        var bop = Date.now();
        return new Promise(async function (resolve, reject) {
            var dbCollection = db.collection(dbColls.ordersMongo);
            let data = [];
            let qBase = [
                { "$match": { "account": storeName, "marketPlace": "shopify" } },
                { '$addFields': { 'customerFullName': { '$concat': ['$customer.first_name', ' ', '$customer.last_name'] } } },
                { "$addFields": { 'itemCount': { $size: "$line_items" } } },
            ];

            var qProject = {
                _id: 0, id: 1, checkout_id: 1, itemCount: 1, name: 1, created_at: 1, updated_at: 1, confirmed: 1, cancelled_at: 1, closed_at: 1, fulfillment_status: 1,
                // current_total_price: 1, current_total_tax: 1, contact_email: 1, presentment_currency: 1, 
                cID: "$customer.id", cEmail: "$customer.email", cPhone: "$customer.phone",
                cEmail2: "$email", cPhone2: "$phone", cPhoneShip: "$shipping_address.phone",
                cFullName: "$customerFullName", cFullNameShip: "$shipping_address.name", cCity: "$shipping_address.city",
                cLat: "$shipping_address.latitude", cLng: "$shipping_address.longitude",
                order_status_url: 1, order_number: 1,
                // invoicerUpdatedAt: 1, invoicerCreatedAt: 1,
                currency: 1, financial_status: 1, total_discounts: 1, total_line_items_price: 1, total_price: 1, total_tax: 1,
            }

            qBase.push({ "$project": qProject });

            qBase.push(
                {
                    $addFields: {
                        idString: { $toString: "$id" }
                    },
                },
                {
                    $lookup: {
                        from: dbColls.invoiceTransactionsMongo, // "cust.invoices",
                        let: { id: "$idString" },
                        pipeline: [
                            { "$addFields": { "orderid": { "$toString": "$orderid" } } },
                            { $match: { $expr: { $eq: ["$orderid", "$$id"] } } }, //, "invoiceStatus": 1
                            // { $project: { _id: 0, }, },
                            { "$addFields": { "belgeNumarasi": "$draftInvoiceDetails.belgeNumarasi" }, },
                                { "$lookup": {
                                        "from": dbColls.invoicesMongo, "let": { "id": "$belgeNumarasi" }, "pipeline": [{ "$match": { "$expr": { "$eq": ["$belgeNumarasi", "$$id"] } } },
                                        { "$project": { "_id": 0, } }], "as": "invoicesX"
                                    } },
                                { $addFields: { draftInvoiceDetails: { $first: "$invoicesX" } } },
                                { "$project": { _id: 0, "invoicesX": 0 } },
                        ],
                        as: "invoices",
                    }
                },
                {
                    $addFields: {
                        invoiceCount: { $size: "$invoices" }
                    },
                },
                { $project: { invoices: 0, idString: 0 } },
            );

            let recCountStg;
            recCountStg = await dbCollection.aggregate([...qBase, { $count: "records" }]).toArray()
            let recCount = recCountStg && Array.isArray(recCountStg) && recCountStg.length !== 0 ? recCountStg[0]?.records : 0;


            var qSort = {}
            if (sortBy) {
                qSort[sortBy] = sortType == 1 ? 1 : -1
                qBase.push({ "$sort": qSort });
            }
            offset && qBase.push({ $skip: parseFloat(offset) })
            limit && qBase.push({ $limit: parseFloat(limit) })


            data = await dbCollection.aggregate([...qBase]).toArray()

            const totalPages = Math.ceil(recCount / limit);
            const currentPage = Math.ceil(offset / recCount * totalPages) ? (Math.ceil(offset / recCount * totalPages)) : 1;

            // log('qBase', JSON.stringify(qBase))

            // modeDebug && log('qBase', JSON.stringify(qBase))
            // modeDebug && logger.info('recCount', recCount)
            // modeDebug && logger.info('currentPage', currentPage)
            // modeDebug && logger.info('totalPages', totalPages)
            // modeDebug && logger.info('listOrders', limit, offset)
            resolve({
                data: data,
                paging: {
                    total: recCount,
                    page: currentPage,
                    pages: totalPages,
                },
            });
        });
    },
    _listOrder: async ({ db, storeName = 'uniqeravintage', dir = 'data', modeDebug = false, reqQuery, userInitials }) => {
        const { slug, sortBy = 'id', sortType = -1, fields } = reqQuery
        
        const orderID = Array.isArray(slug) && slug[1] ? slug[1] : false

        var bop = Date.now();
        return new Promise(async function (resolve, reject) {
            var dbCollection = db.collection(dbColls.ordersMongo);
            let data = [];
            let qBase = [
                { "$match": { "account": storeName, "marketPlace": "shopify", id: parseInt(orderID) || 0 } },
                { '$addFields': { 'customerFullName': { '$concat': ['$customer.first_name', ' ', '$customer.last_name'] } } },
                { "$addFields": { 'itemCount': { $size: "$line_items" } } },
            ];

            var qProject = {
                _id: 0, id: 1, checkout_id: 1, itemCount: 1, name: 1, created_at: 1, updated_at: 1, confirmed: 1, cancelled_at: 1, closed_at: 1, fulfillment_status: 1,
                // current_total_price: 1, current_total_tax: 1, contact_email: 1, presentment_currency: 1, 
                cID: "$customer.id", cEmail: "$customer.email", cPhone: "$customer.phone",
                cEmail2: "$email", cPhone2: "$phone", cPhoneShip: "$shipping_address.phone",
                cFullName: "$customerFullName", cFullNameShip: "$shipping_address.name", cCity: "$shipping_address.city",
                cLat: "$shipping_address.latitude", cLng: "$shipping_address.longitude",
                order_status_url: 1, order_number: 1,
                // invoicerUpdatedAt: 1, invoicerCreatedAt: 1,
                currency: 1, financial_status: 1, total_discounts: 1, total_line_items_price: 1, total_price: 1, total_tax: 1,
            }

            qBase.push({ "$project": qProject });

            qBase.push(
                {
                    $addFields: {
                        idString: { $toString: "$id" }
                    },
                },
                {
                    $lookup: {
                        from: dbColls.invoiceTransactionsMongo, // "cust.invoices",
                        let: { id: "$idString" },
                        pipeline: [
                            { "$addFields": { "orderid": { "$toString": "$orderid" } } },
                            { $match: { $expr: { $eq: ["$orderid", "$$id"] } } }, //, "invoiceStatus": 1
                            // { $project: { _id: 0, }, },
                            { "$addFields": { "belgeNumarasi": "$draftInvoiceDetails.belgeNumarasi" }, },
                                { "$lookup": {
                                        "from": dbColls.invoicesMongo, "let": { "id": "$belgeNumarasi" }, "pipeline": [{ "$match": { "$expr": { "$eq": ["$belgeNumarasi", "$$id"] } } },
                                        { "$project": { "_id": 0, } }], "as": "invoicesX"
                                    } },
                                { $addFields: { draftInvoiceDetails: { $first: "$invoicesX" } } },
                                { "$project": { _id: 0, "invoicesX": 0 } },
                        ],
                        as: "invoices",
                    }
                },
                {
                    $addFields: {
                        invoiceCount: { $size: "$invoices" }
                    },
                },
                { $project: { invoices: 0, idString: 0 } },
            );

            data = await dbCollection.aggregate([...qBase]).toArray()

            resolve({
                data: data ? data[0] : false,
                orderID,
            });
        });
    },
    _getOrder: async ({ db, storeName = 'uniqeravintage', dir = 'data', modeDebug = false, reqQuery, userInitials }) => {
        const { slug } = reqQuery
        const orderid = slug[1];
        var bop = Date.now();
        return new Promise(async function (resolve, reject) {
            if (orderid) {

                var dbCollection = db.collection(dbColls.ordersMongo);
                let data = [];
                let qBase = [
                    { "$match": { "account": storeName, "marketPlace": "shopify", id: parseFloat(orderid) } },
                    { '$addFields': { 'customerFullName': { '$concat': ['$customer.first_name', ' ', '$customer.last_name'] } } }
                ];

                qBase.push(
                    {
                        $addFields: {
                            idString: { $toString: "$id" }
                        },
                    },
                    {
                        $lookup: {
                            from: dbColls.invoiceTransactionsMongo, //"cust.invoices",
                            let: { id: "$idString" },
                            pipeline: [
                                // { "$addFields": { "orderid": "$refOrderData.id" }, },
                                { "$addFields": { "orderid": { "$toString": "$orderid" } } },
                                { $match: { $expr: { $eq: ["$orderid", "$$id"] } } },
                                // { $project: { _id: 0, }, },
                                { "$addFields": { "belgeNumarasi": "$draftInvoiceDetails.belgeNumarasi" }, },
                                { $addFields: { draftFirstInvoiceDetails: "$draftInvoiceDetails" } } ,
                                { "$lookup": {
                                        "from": dbColls.invoicesMongo, "let": { "id": "$belgeNumarasi" }, "pipeline": [{ "$match": { "$expr": { "$eq": ["$belgeNumarasi", "$$id"] } } },
                                        { "$project": { "_id": 0, } }], "as": "invoicesX"
                                    } },
                                { $addFields: { draftInvoiceDetails: { $first: "$invoicesX" } } },
                                { "$project": { _id: 0, "invoicesX": 0 } },
                            ],
                            as: "invoices",
                        }
                    },
                    {
                        $addFields: {
                            invoiceCount: { $size: "$invoices" }
                        },
                    },
                    { $project: { idString: 0 } },
                );

                // log('qbasse', JSON.stringify(qBase))
                data = await dbCollection.aggregate([...qBase]).toArray()

                resolve({
                    data: data && Array.isArray(data) ? data[0] : {},
                    paging: {
                        total: 1,
                        page: 1,
                        pages: 1,
                    },
                });
            } else {

            }
        });
    },

    getDBLastOrderUpdatedTime: async ({ db, targetColl = dbColls.ordersMongo, modeDebug }) => {
        var coll = db.collection(targetColl);
        let q = [{ $group: { _id: null, max: { $max: "$sourceUpdatedAt" } } },
        { $project: { _id: 0 } }
        ];
        try {
            let respStg = await coll.aggregate(q).toArray();
            if (Array.isArray(respStg) && respStg.length !== 0) {
                modeDebug && log('getDBLastOrderUpdatedTime', respStg[0]?.max)
                return respStg[0]?.max ? new Date(respStg[0]?.max).toISOString() : new Date().toISOString(); // return "2024-01-03T14:35:06.000Z" "2023-12-01T14:35:06.000Z"
            } else {
                return "2019-12-01T14:35:06.000Z" //"2024-10-20T14:35:06.000Z" //
            }
        } catch (e) {
            return "2024-10-10T14:35:06.000Z"
        }
    },

    fetchOrdersubFn: async ({ ordersURL, modeDebug, dataName = 'orders' }) => {
        return new Promise(async function (resolve, reject) {
            modeDebug && log('fetch url', ordersURL)
            axios.get(ordersURL)
                .then(async (res) => {
                    try {
                        let data = res.data ? res.data[dataName] : []
                        resolve(data);
                    } catch (e1) {
                        modeDebug && log('fetch error', e1)
                        resolve([]);
                    }
                }).catch(e => {
                    modeDebug && log('url', ordersURL, e)
                });
        });
    },

    fetchOrderTransactions: async ({ db, storeName = 'uniqeravintage', orderID = null, modeDebug = false, refTime = null, reqQuery }) => {
        if (!process.env.SHOPIFYBASE) {
            throw new Error('Please add your SHOPIFY URI to .env.local 1');
        }
        const shopifyBase = process.env.SHOPIFYBASE;
        var urlBase = shopifyBase + "/admin/api/2023-10/orders/";
        urlBase += orderID + '/transactions.json';

        return new Promise(async function (resolve, reject) {
            if (orderID) {
                try {
                    let dataStg = await shopify.fetchOrdersubFn({
                        ordersURL: urlBase, modeDebug,
                        dataName: 'transactions',
                    });
                    resolve(dataStg);
                }
                catch (e) {
                    modeDebug && log('error', e)
                    reject(false)
                }
            } else {
                modeDebug && log('error - noOrderID')
                reject(false)
            }
        });

    },
    fetchOrders: async ({ db, storeName = 'uniqeravintage',
        modeDebug = false, refTime = null,
        reqQuery, fetchTransactions = false }) => {
        // var ordersURL = appvars.urls.shopify.orders
        if (!process.env.SHOPIFYBASE) {
            throw new Error('Please add your SHOPIFY URI to .env.local');
        }
        const shopifyBase = process.env.SHOPIFYBASE;
        const ordersURL = shopifyBase + '/admin/api/2024-01/orders.json?status=any'
        var bop = Date.now();
        return new Promise(async function (resolve, reject) {
            let keepOn = true;
            let cipa = null;
            let tBOPfrom = reqQuery?.from ? reqQuery?.from : 'last';
            let refDBCipaStg;
            let refDBCipa; 
            let purgeDBMode = 0;
            if (tBOPfrom === 'last') {
                // refDBCipaStg = tBOPfrom === 'last' ?  : Date.now();
                tBOPfrom = await shopify.getDBLastOrderUpdatedTime({ db });
                refDBCipa = tBOPfrom;
            } else if (tBOPfrom === 'all') {
                refDBCipa = "2010-12-01T14:35:06.000Z"; //2022-01-11 10:38:52.000Z
                purgeDBMode = 1;
            } else {
                //custom date!
                refDBCipa = new Date(tBOPfrom).toISOString();
                purgeDBMode = 2; //partial delete.. delete before tBOPfrom!?
            }
            
            // log('fetchOrders - reqQuery tBOPfrom?', reqQuery, tBOPfrom, ) 
            // log('fetchOrders - refDBCipa', refDBCipa)

            let sayac = 0;
            let data = [];
            while (keepOn) {
                let ActURL = ordersURL + '&updated_at_max=' + (cipa ? new Date(parseFloat(cipa)).toISOString() : new Date(Date.now()).toISOString());
                ActURL += '&updated_at_min=' + refDBCipa;
                let dataStg = await shopify.fetchOrdersubFn({
                    ordersURL: ActURL, modeDebug,
                });
                if (Array.isArray(dataStg) && dataStg.length !== 0) {
                    let maxDate = Math.min(...dataStg.map(x => new Date(x["updated_at"])))
                    if (cipa !== maxDate) {
                        cipa = maxDate ? maxDate : null;
                        data = [...data, ...dataStg];
                        sayac++;
                        // true && log(sayac, 'dataLength', data.length);
                        // true && console.log(' ');
                    } else {
                        keepOn = false
                    }
                } else {
                    keepOn = false
                }
            }
            if (!keepOn) {
                try {
                    if (Array.isArray(data) && data.length !== 0) {
                        const filePath = path.resolve(process.cwd(), './src/lib/bins/' + storeName + '_orders' + Date.now().toString() + '.json');
                        false && await main.save2file({ data, filePath, modeDebug: false });
                        if (purgeDBMode == 1) {
                            await db.collection(dbColls.ordersMongo).deleteMany({}) //.exec()
                        } else {
                            let data2Remove = [...data].map(d => d.id);
                            let removeQ = { id: { $in: data2Remove } };
                            await db.collection(dbColls.ordersMongo).deleteMany(removeQ) //.exec()
                        }
                        let rown = 0;
                        let data2Save = []
                        for (let d of data) {
                            rown++;
                            d.lastSourceSyncAt = new Date(Date.now());
                            d.account = storeName;
                            d.marketPlace = 'shopify';
                            try {
                                d.sourceCreatedAt = d.created_at ? new Date(d.created_at) : null;
                                d.sourceUpdatedAt = d.updated_at ? new Date(d.updated_at) : null;
                            }
                            catch (a1) { }
                            // d.Test = 'success';
                            if (fetchTransactions) {
                                try {
                                    d.transactions = await shopify.fetchOrderTransactions({
                                        orderID: d.id,
                                        account: storeName,
                                        modeDebug,
                                    })
                                }
                                catch (a2) { }
                            }
                            data2Save.push(d);
                        };
                        const options = { ordered: true };
                        await db.collection(dbColls.ordersMongo).insertMany(data2Save, options)

                        modeDebug && log('DB records', data.length);
                        resolve({ data: data, status: 200, sure: Date.now() - bop });
                    } else {
                        resolve({ data: data, status: 200, sure: Date.now() - bop });
                    }
                } catch (e1) {
                    modeDebug && log('save error', e1)
                    reject(false)
                }
            } else {
                modeDebug && log('working')
            }
            // resolve(tBOPfrom)
        });
    },
    fetchOrdersx: async ({ db, storeName = 'uniqeravintage',
        modeDebug = false, refTime = null,
        reqQuery, fetchTransactions = false }) => {
        // var ordersURL = appvars.urls.shopify.orders
        if (!process.env.SHOPIFYBASE) {
            throw new Error('Please add your SHOPIFY URI to .env.local');
        }
        const shopifyBase = process.env.SHOPIFYBASE;
        const ordersURL = shopifyBase + '/admin/api/2024-01/orders.json?status=any'
        var bop = Date.now();
        return new Promise(async function (resolve, reject) {
            let keepOn = true;
            let cipa = null;
            const tBOPfrom = reqQuery?.from ? reqQuery?.from : 'last';
            let refDBCipaStg = await shopify.getDBLastOrderUpdatedTime({ db });
            const refDBCipa = tBOPfrom === 'last' ? refDBCipaStg : "2010-12-01T14:35:06.000Z"; //2022-01-11 10:38:52.000Z
            
            log('fetchOrders - reqQuery, lastDate?', reqQuery, tBOPfrom, refDBCipaStg, refDBCipa)

            let sayac = 0;
            let data = [];
            while (keepOn) {
                let ActURL = ordersURL + '&updated_at_max=' + (cipa ? new Date(parseFloat(cipa)).toISOString() : new Date(Date.now()).toISOString());
                ActURL += '&updated_at_min=' + refDBCipa;
                let dataStg = await shopify.fetchOrdersubFn({
                    ordersURL: ActURL, modeDebug,
                });
                if (Array.isArray(dataStg) && dataStg.length !== 0) {
                    let maxDate = Math.min(...dataStg.map(x => new Date(x["updated_at"])))
                    if (cipa !== maxDate) {
                        cipa = maxDate ? maxDate : null;
                        data = [...data, ...dataStg];
                        sayac++;
                        // true && log(sayac, 'dataLength', data.length);
                        // true && console.log(' ');
                    } else {
                        keepOn = false
                    }
                } else {
                    keepOn = false
                }
            }
            if (!keepOn) {
                try {
                    if (Array.isArray(data) && data.length !== 0) {
                        const filePath = path.resolve(process.cwd(), './src/lib/bins/' + storeName + '_orders' + Date.now().toString() + '.json');
                        false && await main.save2file({ data, filePath, modeDebug: false });
                        let data2Remove = [...data].map(d => d.id)
                        let removeQ = { id: { $in: data2Remove } };
                        await db.collection(dbColls.ordersMongo).deleteMany(removeQ) //.exec()

                        let rown = 0;
                        let data2Save = []
                        for (let d of data) {
                            rown++;
                            d.lastSourceSyncAt = new Date(Date.now());
                            d.account = storeName;
                            d.marketPlace = 'shopify';
                            try {
                                d.sourceCreatedAt = d.created_at ? new Date(d.created_at) : null;
                                d.sourceUpdatedAt = d.updated_at ? new Date(d.updated_at) : null;
                            }
                            catch (a1) { }
                            // d.Test = 'success';
                            if (fetchTransactions) {
                                try {
                                    d.transactions = await shopify.fetchOrderTransactions({
                                        orderID: d.id,
                                        account: storeName,
                                        modeDebug,
                                    })
                                }
                                catch (a2) { }
                            }
                            data2Save.push(d);
                        };
                        const options = { ordered: true };
                        await db.collection(dbColls.ordersMongo).insertMany(data2Save, options)

                        modeDebug && log('DB records', data.length);
                        resolve({ data: data, status: 200, sure: Date.now() - bop });
                    } else {
                        resolve({ data: data, status: 200, sure: Date.now() - bop });
                    }
                } catch (e1) {
                    modeDebug && log('save error', e1)
                    reject(false)
                }
            } else {
                modeDebug && log('working')
            }


        });
    },
}
export const sqlite = {
    createTables: ({ sqlClient, target, dropCreate = false }) => {
        const q_fact_orders = `
            create table IF NOT EXISTS ${dbColls.orders} ( 
                [id] int not null DEFAULT(0),
                [cancelled_at] text NOT NULL DEFAULT(''),
                [checkout_id] int not null DEFAULT(0),
                [closed_at] text NOT NULL DEFAULT(''),
                [confirmed] bool not null DEFAULT(true),
                [created_at] text NOT NULL DEFAULT(''),
                [currency] text NOT NULL DEFAULT(''),
                [financial_status] text NOT NULL DEFAULT(''),
                [fulfillment_status] text NOT NULL DEFAULT(''),
                [name] text NOT NULL DEFAULT(''),
                [order_number] int not null DEFAULT(0),
                [order_status_url] text NOT NULL DEFAULT(''),
                [total_discounts] REAL NOT NULL DEFAULT(0),
                [total_line_items_price] REAL NOT NULL DEFAULT(0),
                [total_price] REAL NOT NULL DEFAULT(0),
                [total_tax] REAL NOT NULL DEFAULT(0),
                [updated_at] text NOT NULL DEFAULT(0),
                [itemCount] int not null DEFAULT(0),
                [cID] int not null DEFAULT(0),
                [cEmail] text NOT NULL DEFAULT(''),
                [cPhone] text NOT NULL DEFAULT(''),
                [cEmail2] text NOT NULL DEFAULT(''),
                [cPhone2] text NOT NULL DEFAULT(''),
                [cPhoneShip] text NOT NULL DEFAULT(''),
                [cFullName] text NOT NULL DEFAULT(''),
                [cFullNameShip] text NOT NULL DEFAULT(''),
                [cCity] text NOT NULL DEFAULT(''),
                [cLat] REAL,
                [cLng] REAL
                dtcreated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP),
                dtupdated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP)
                );
        `;

        return new Promise(async (resolve, reject) => {
            try {
                if (target === `q_${dbColls.orders}`) {
                    let sql = q_fact_orders;
                    if (dropCreate) {
                        var sqlq = `DROP TABLE if exists ${dbColls.orders}`;
                        sqlClient.exec(sqlq);
                    }
                    try {
                        var res = sqlClient.exec(sql);
                        resolve(true)
                    }
                    catch (e) {
                        log(sql);
                        reject(e)
                    }
                }
                resolve(true)
            } catch (e) {
                log('error: fnDB_createTables ', e)
                reject(e)
            }
        });
    },
    insertManyJSON: async ({
        db, rawData, tableName, time1Field = null,
        addBatchID = false,
        time2Field = null, debug = false }) => {
        return new Promise(async (resolve, reject) => {
            try {
                let arr = Array.isArray(rawData) ? rawData : await json2arr(rawData);
                if (addBatchID) {
                    let batchid = Date.now();
                    let sq_pre = `update ${tableName} set is_deleted = true where batchid not in (${batchid})`;
                    db.exec(sq_pre);

                    Array.isArray(arr) && arr.map(a => {
                        a.batchid = batchid;
                        delete a.lastUpdateId;
                        if (time1Field) {
                            a[time1Field + 'HRF'] = new Date(a[time1Field]).toISOString();
                        }
                        if (time2Field) {
                            a[time2Field + 'HRF'] = new Date(a[time2Field]).toISOString();
                        }
                    })
                }
                let sql = await generateSQL({ payload: arr, table: tableName });
                debug && console.log('sql', sql)
                var res = db.exec(sql);
                resolve(res);
            }
            catch (e) {
                reject(e)
            }
        });
    },
    setSqlDB: async ({ db, sqlClient, storeName = 'uniqeravintage', dir = 'data', modeDebug = false, reqQuery, userInitials }) => {
        const { slug, sortBy = 'id', sortType = -1, fields } = reqQuery
        const limit = Array.isArray(slug) && slug[1] ? slug[1] : 1500
        const offset = Array.isArray(slug) && slug[2] ? slug[2] : 0

        var bop = Date.now();
        return new Promise(async function (resolve, reject) {

            await sqlite.createTables({
                sqlClient, target: `q_${dbColls.orders}`, dropCreate: true,
            });

            var dbCollection = db.collection(dbColls.ordersMongo);
            let data = [];
            let qBase = [
                { "$match": { "account": storeName, "marketPlace": "shopify" } },
                { '$addFields': { 'customerFullName': { '$concat': ['$customer.first_name', ' ', '$customer.last_name'] } } },
                { "$addFields": { 'itemCount': { $size: "$line_items" } } },
            ];
            var qProject = {
                _id: 0, id: 1, checkout_id: 1, itemCount: 1, name: 1, created_at: 1, updated_at: 1, confirmed: 1, cancelled_at: 1, closed_at: 1, fulfillment_status: 1,
                // current_total_price: 1, current_total_tax: 1, contact_email: 1, presentment_currency: 1, 
                cID: "$customer.id", cEmail: "$customer.email", cPhone: "$customer.phone",
                cEmail2: "$email", cPhone2: "$phone", cPhoneShip: "$shipping_address.phone",
                cFullName: "$customerFullName", cFullNameShip: "$shipping_address.name", cCity: "$shipping_address.city",
                cLat: "$shipping_address.latitude", cLng: "$shipping_address.longitude",
                order_status_url: 1, order_number: 1,
                // invoicerUpdatedAt: 1, invoicerCreatedAt: 1,
                currency: 1, financial_status: 1, total_discounts: 1, total_line_items_price: 1, total_price: 1, total_tax: 1,
            }
            qBase.push({ "$project": qProject });
            var qSort = {}
            if (sortBy) {
                qSort[sortBy] = sortType == 1 ? 1 : -1
                qBase.push({ "$sort": qSort });
            }
            offset && qBase.push({ $skip: parseFloat(offset) })
            limit && qBase.push({ $limit: parseFloat(limit) })

            data = await dbCollection.aggregate([...qBase]).toArray();
            sqlite.insertManyJSON({
                db: sqlClient,
                rawData: data,
                tableName: `${dbColls.orders}`,
                debug: true,
            });
            modeDebug && log('qBase', JSON.stringify(qBase))
            resolve({
                data: Date.now() - bop,
            });
        });
    }
}
export const sqlite_orders = {
    listorders: async ({ db, battleID }) => {
        return new Promise(async (resolve, reject) => {
            try {
                let qq = `  
                        SELECT *
                        FROM ${dbColls.orders};
                        `;
                let task = await db.prepare(qq).all();
                if (task && Array.isArray(task) && task.length !== 0) {
                    resolve({ data: task });
                } else {
                    reject('error: no record')
                }
            }
            catch (e) {
                console.log('error: nodes getBattle', e)
                reject(e)
            }
        });
    },
}

const generateSQL = async ({ payload, table, setNulls = true }) => {
    // 
    let ssql = '';
    let arr = payload;
    return new Promise(async (resolve, reject) => {
        for (const a of arr) {
            let pl = await getJSONFields(a);
            ssql = ssql + 'INSERT INTO ' + table + ' (' + pl.fields.toString() + ') VALUES (' + pl.fieldValues.toString() + ');\n\n'
        }
        resolve(ssql)
    });
};
const getJSONFields = payload => {
    return new Promise(async (resolve, reject) => {
        let resp = {}
        let fields = [];
        let fieldValues = [];
        let tmp = JSON.parse(JSON.stringify(payload))
        try {
            for (const key in tmp) {
                if (tmp.hasOwnProperty(key)) {
                    fields.push(key);
                    fieldValues.push(typeof (tmp[key]) === 'string' ? "'" + payload[key] + "'" : (payload[key] ? payload[key] : "''"));
                }
            }
            resp = { fields, fieldValues };
            resolve(resp);
        }
        catch (e) {
            reject(e);
        }
    });
}
const json2arr = dt => {
    let arr = [];
    return new Promise(async (resolve, reject) => {
        if (dt) {
            try {
                for (const key in dt) {
                    if (dt.hasOwnProperty(key)) {
                        arr.push(dt[key]);
                        //   console.log(`${key} : ${JSON.stringify(dt[key])}`)
                    }
                }
                resolve(arr)
            }
            catch (e) {
                reject(e)
            }
        } else {
            reject('no value')
        }
    });
}
function createMarketID(storeName, invData) {
    let resp = ""
        resp += storeName;
        resp += '|';
        resp += invData?.phoneNumber;
        resp += '|';
        resp += invData?.name.replaceAll('Ğ', 'g')
            .replaceAll('Ü', 'u')
            .replaceAll('Ş', 's')
            .replaceAll('I', 'i')
            .replaceAll('İ', 'i')
            .replaceAll('Ö', 'o')
            .replaceAll('Ç', 'c')
            .replaceAll('ğ', 'g')
            .replaceAll('ü', 'u')
            .replaceAll('ş', 's')
            .replaceAll('ı', 'i')
            .replaceAll('ö', 'o')
            .replaceAll('ç', 'c').replace(/[\W_]+/g, "");
        resp += '|';
        resp += invData?.email;
    return resp;
}
function createInvoiceInitials(invData) {
    var uid = uuid();

    let sampleInvoice = {
        uuid: uid,
        date: invData.date,
        time: invData.time,
        taxIDOrTRID: invData.taxIDOrTRID,
        taxOffice: invData.taxOffice,
        title: invData.title,
        name: invData.name,
        surname: invData.surname,
        fullAddress: invData.fullAddress,
        district: invData.district,
        city: invData.city,
        email: invData.email,
        phoneNumber: invData.phoneNumber,
        items: invData.item,
        totalVAT: invData.totalVAT,
        grandTotal: invData.grandTotal,
        grandTotalInclVAT: invData.grandTotalInclVAT,
        paymentTotal: invData.grandTotalInclVAT
      }

    return sampleInvoice
}