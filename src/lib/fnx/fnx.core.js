
const fs = require('fs').promises;
const fss = require('fs');
const path = require('path');
const { writeFileSync } = fss;
const loggin = true

const log = exports.log = (...arg) => {
  loggin && console.log((new Date()).toISOString(), 'INFO', ...arg);
}

const chunkify = exports.chunkify = (a, n, balanced) => {
  //array splitter...
  if (n < 2)
    return [a];

  var len = a.length,
    out = [],
    i = 0,
    size;

  if (len % n === 0) {
    size = Math.floor(len / n);
    while (i < len) {
      out.push(a.slice(i, i += size));
    }
  }

  else if (balanced) {
    while (i < len) {
      size = Math.ceil((len - i) / n--);
      out.push(a.slice(i, i += size));
    }
  }

  else {

    n--;
    size = Math.floor(len / n);
    if (len % size === 0)
      size--;
    while (i < size * n) {
      out.push(a.slice(i, i += size));
    }
    out.push(a.slice(size * n));

  }

  return out;
}

const main = (exports.main = {
  debug: (...arg) => {
    console.log((new Date()).toISOString(), 'DEBUG', ...arg);
  },
  info: (...arg) => {
    console.log((new Date()).toISOString(), 'INFO', ...arg);
  },
  sleep: (ms = 300) => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
  promiseTimeout: (promise, connTimeOut = 10000) => {
    let timeout = new Promise((resolve, reject) => {
      let id = setTimeout(() => {
        clearTimeout(id);
        reject('promiseTimeout in ' + connTimeOut + 'ms.', promise);
      }, connTimeOut);
    });
    // Returns a race between our timeout and the passed in promise
    return Promise.race([promise, timeout]);
  },
  // generateKey: ({ inclTime = false }) => {
  //   let randStr = (+new Date * Math.random()).toString(36).substring(0, 6);
  //   let resp = inclTime ? Date.now().toString() + '-' + randStr : randStr;
  //   return resp
  // },
  save2filePublic: async (dt, filename, debg = false) => {
    let bop = Date.now()
    return new Promise(async function (resolve, reject) {
      let ffilename;
      try {
        ffilename = path.join(process.cwd(), "public/assets/" + filename)
        var res = await fs.writeFile(ffilename, JSON.stringify(dt), 'utf8');
        debg && console.log("data is saved.", ffilename, Date.now() - bop);
        resolve(res)
      } catch (err) {
        log('dosya kadedilemedi.', ffilename, err)
        reject(err);
      }
    })
  },

  save2file: async ({data, filePath, modeDebug = false}) => {
    let bop = Date.now()
    return new Promise(async function (resolve, reject) {
      try {
        const basePath = path.resolve(process.cwd(), filePath);
        var res = await fs.writeFile(filePath, JSON.stringify(data), 'utf8');
        modeDebug && console.log("data is saved.", basePath, filePath, Date.now() - bop);
        resolve(res)
      } catch (err) {
        log('dosya kadedilemedi.', filePath, err)
        reject(err);
      }
    });
  },

  save2file_: async ({data2save, folder = '', filename = 'tmp.json', modeDebug = false}) => {
    const basedir = path.resolve(process.cwd());
    const basePath = basedir + '/' + folder
    return new Promise(async (resolve) => {
      let data = JSON.stringify(data2save, null, 2);
      var file2s = basePath + '/' + filename;
      modeDebug && log('basePath', file2s)
      try {
        await fs.writeFile(file2s, data);
        modeDebug && log('file saved!');
        resolve(true)
      } catch (e) {
        modeDebug && log('error save file', e)
        resolve(false)
      }
    });
  },
  // warn: (...arg) => {
  //     console.log(chalk.white.bgRed.bold((new Date()).toISOString(), 'WARN'), ...arg);
  // },
  // success: (...arg) => {
  //     console.log(chalk.white.bgBlue((new Date()).toISOString(), 'SUCCESS'), ...arg);
  // },
  sendEmail: (props) => {
    const { to, subject, textmsg, htmlMsg = false } = props;
    var modeTest = true;
    return new Promise(async (resolve, reject) => {
        var service = 'Yandex';
        var auth = {
            Gmail: {
                user: process.env.GMAIL_EMAIL_ADDRESS, // Your email id
                pass: process.env.GMAIL_APP_PASSWORD // Your password
            },
            Yandex: {
                user: process.env.YANDEX_EMAIL_ADDRESS,  
                pass: process.env.YANDEX_APP_PASSWORD 
            }
        }
        var transporter = nodemailer.createTransport({ service, auth: auth[service], });
        var mailOptions = {
            from: auth[service]['user'],
            to: modeTest ? '<EMAIL>' : to, 
            subject: subject,
            text: textmsg 
        };
        if (htmlMsg) {
            mailOptions.htmlMsg = htmlMsg;
        }
  
        transporter.sendMail(mailOptions, function (error, info) {
            if (error) {
                console.log('error emailing: ', error);
                reject(error)
            } else {
                console.log('Message sent: ' + info.response);
                // return true
                resolve(true)
            };
        });
    })
  
  }
}
);

 