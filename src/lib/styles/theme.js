import { Roboto } from 'next/font/google';
import { createTheme, alpha, getContrastRatio } from '@mui/material/styles';
import { red, grey, lightBlue } from '@mui/material/colors';

const roboto = Roboto({
  weight: ['300', '400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
});

// Create a theme instance.
const theme = createTheme({
  palette: {
    primary: {
      main: '#556cd6',
    },
    secondary: {
      main: '#19857b',
    },
    error: {
      main: red.A400,
    },
  },
  typography: {
    fontFamily: roboto.style.fontFamily,
  },
  background: {
    menuItem: red.A400,
    paper: lightBlue[300],
    paperDark: lightBlue[700],
  },
});

export default theme;