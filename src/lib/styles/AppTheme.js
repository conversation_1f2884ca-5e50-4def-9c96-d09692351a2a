import * as React from "react"
import { ThemeProvider, createTheme } from "@mui/material/styles"
import { inputsCustomizations } from "./customizations/inputs.js"
import { dataDisplayCustomizations } from "./customizations/dataDisplay.js"
import { feedbackCustomizations } from "./customizations/feedback.js"
import { navigationCustomizations } from "./customizations/navigation"
import { surfacesCustomizations } from "./customizations/surfaces"
import { colorSchemes, typography, shadows, shape } from "./themePrimitives"

export default function AppTheme({
  children,
  disableCustomTheme,
  themeComponents
}) {
  const theme = React.useMemo(() => {
    return disableCustomTheme
      ? {}
      : createTheme({
          // For more details about CSS variables configuration, see https://mui.com/material-ui/customization/css-theme-variables/configuration/
          cssVariables: {
            colorSchemeSelector: "data-mui-color-scheme",
            cssVarPrefix: "template"
          },
          colorSchemes, // Recently added in v6 for building light & dark mode app, see https://mui.com/material-ui/customization/palette/#color-schemes
          typography,
          shadows,
          shape,
          components: {
            ...inputsCustomizations,
            ...dataDisplayCustomizations,
            ...feedbackCustomizations,
            ...navigationCustomizations,
            ...surfacesCustomizations,
            ...themeComponents
          }
        })
  }, [disableCustomTheme, themeComponents])
  if (disableCustomTheme) {
    return <React.Fragment>{children}</React.Fragment>
  }
  return (
    <ThemeProvider theme={theme} disableTransitionOnChange>
      {children}
    </ThemeProvider>
  )
}
