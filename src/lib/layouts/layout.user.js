import React, { useEffect, useState, useContext } from 'react'
// import { alpha } from "@mui/material/styles"
import CssBaseline from "@mui/material/CssBaseline"
import Box from "@mui/material/Box"
import Stack from "@mui/material/Stack"
import Container from "@mui/material/Container"
import { ColorModeContext } from "@/lib/context/Provider.themeDarkMode";

import AppNavbar from "@/components/header/user.AppNavbar.js"
import Header from "@/components/header/user.Header.js"
// import MainGrid from "./user.main.grid.js"
import SideMenu from "@/components/header/SideMenu.js"
import AppTheme from "@/lib/styles/AppTheme"

import {
    chartsCustomizations,
    dataGridCustomizations,
    datePickersCustomizations,
    treeViewCustomizations
} from "@/lib/styles/customizations"

const xThemeComponents = {
    ...chartsCustomizations,
    ...dataGridCustomizations,
    ...datePickersCustomizations,
    ...treeViewCustomizations
}

export default function Layout(props) {

    const { children, session, bread, pgTitle, pgBread, headerPosition = 'fixed', headerBgColor, ...rest } = props;
    const { user } = session ? session : {};
    const role = user ? user.role : null
    const isAdmin = user ? user?.roles?.isAdmin : null
    const { token, refreshToken } = user ? user : {};
    const { mode, colorMode, isMobile, isTablet } = useContext(ColorModeContext);

    return (
        <AppTheme {...props} themeComponents={xThemeComponents}>
            <CssBaseline enableColorScheme />
            <Box sx={{
                display: "flex",
                width: '100%',
                minHeight: "100vh",
                bgcolor: 'background.default',
                color: 'text.primary',
            }}>
                <SideMenu {...props} session={session} />
                <AppNavbar />
                {/* Main content */}
                <Box
                    component="main"
                    sx={theme => ({
                        flexGrow: 1,
                        backgroundColor: theme.vars
                            ? `rgba(${theme.vars.palette.background.defaultChannel} / 1)`
                            : alpha(theme.palette.background.default, 1),
                        overflow: "auto"
                    })}
                >
                    <Header {...props}
                        position={headerPosition}
                        bgcolor={headerBgColor}
                    >
                        {props.headerItems}
                    </Header>
                    <Container
                        spacing={2}
                        maxWidth={false}
                        disableGutters={true}
                        sx={{
                            alignItems: "center",
                            // borderWidth: 1,
                            // width: '100%',
                            //   mx: 3,
                            pb: 5,
                            mt: { xs: 0, md: 0 },
                        }}
                    >
                        {children}
                    </Container>
                </Box>
            </Box>
        </AppTheme>
    )
}
